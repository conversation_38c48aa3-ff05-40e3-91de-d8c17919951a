// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/descriptions/demo/basic.tsx extend context correctly 1`] = `
<div
  class="ant-descriptions css-var-test-id"
>
  <div
    class="ant-descriptions-header"
  >
    <div
      class="ant-descriptions-title"
    >
      User Info
    </div>
  </div>
  <div
    class="ant-descriptions-view"
  >
    <table>
      <tbody>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                UserName
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                <PERSON>
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Telephone
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                1810000000
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Live
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                Hangzhou, Zhejiang
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Remark
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                empty
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Address
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                No. 18, Wantang Road, Xihu District, Hangzhou, Zhejiang, China
              </span>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`renders components/descriptions/demo/basic.tsx extend context correctly 2`] = `[]`;

exports[`renders components/descriptions/demo/block.tsx extend context correctly 1`] = `
<div
  class="ant-descriptions ant-descriptions-bordered css-var-test-id"
>
  <div
    class="ant-descriptions-header"
  >
    <div
      class="ant-descriptions-title"
    >
      User Info
    </div>
  </div>
  <div
    class="ant-descriptions-view"
  >
    <table>
      <tbody>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              UserName
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              Zhou Maomao
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Live
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              Hangzhou, Zhejiang
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Remark
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              empty
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Address
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              No. 18, Wantang Road, Xihu District, Hangzhou, Zhejiang, China
            </span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`renders components/descriptions/demo/block.tsx extend context correctly 2`] = `[]`;

exports[`renders components/descriptions/demo/border.tsx extend context correctly 1`] = `
<div
  class="ant-descriptions ant-descriptions-bordered css-var-test-id"
>
  <div
    class="ant-descriptions-header"
  >
    <div
      class="ant-descriptions-title"
    >
      User Info
    </div>
  </div>
  <div
    class="ant-descriptions-view"
  >
    <table>
      <tbody>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Product
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              Cloud Database
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Billing Mode
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              Prepaid
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Automatic Renewal
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              YES
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Order time
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              2018-04-24 18:00:00
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Usage Time
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              2019-04-24 18:00:00
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Status
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              <span
                class="ant-badge ant-badge-status ant-badge-not-a-wrapper css-var-test-id"
              >
                <span
                  class="ant-badge-status-dot ant-badge-status-processing"
                />
                <span
                  class="ant-badge-status-text"
                >
                  Running
                </span>
              </span>
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Negotiated Amount
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              $80.00
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Discount
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              $20.00
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Official Receipts
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              $60.00
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Config Info
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              Data disk type: MongoDB
              <br />
              Database version: 3.4
              <br />
              Package: dds.mongo.mid
              <br />
              Storage space: 10 GB
              <br />
              Replication factor: 3
              <br />
              Region: East China 1
              <br />
            </span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`renders components/descriptions/demo/border.tsx extend context correctly 2`] = `
[
  "Warning: [antd: Descriptions] Sum of column \`span\` in a line not match \`column\` of Descriptions.",
]
`;

exports[`renders components/descriptions/demo/component-token.tsx extend context correctly 1`] = `
<div>
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-wrapper ant-radio-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-checked"
      >
        <input
          checked=""
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="default"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        default
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="middle"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        middle
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="small"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        small
      </span>
    </label>
  </div>
  <br />
  <br />
  <div
    class="ant-descriptions ant-descriptions-bordered css-var-test-id"
  >
    <div
      class="ant-descriptions-header"
    >
      <div
        class="ant-descriptions-title"
      >
        Custom Size
      </div>
      <div
        class="ant-descriptions-extra"
      >
        <div>
          extra color: blue
        </div>
      </div>
    </div>
    <div
      class="ant-descriptions-view"
    >
      <table>
        <tbody>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
            >
              <span>
                Product
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
            >
              <span>
                Cloud Database
              </span>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
            >
              <span>
                Billing
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
            >
              <span>
                Prepaid
              </span>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
            >
              <span>
                Time
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
            >
              <span>
                18:00:00
              </span>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
            >
              <span>
                Amount
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
            >
              <span>
                $80.00
              </span>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
            >
              <span>
                Discount
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
            >
              <span>
                $20.00
              </span>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
            >
              <span>
                Official
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
            >
              <span>
                $60.00
              </span>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
            >
              <span>
                Config Info
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
            >
              <span>
                Data disk type: MongoDB
                <br />
                Database version: 3.4
                <br />
                Package: dds.mongo.mid
                <br />
                Storage space: 10 GB
                <br />
                Replication factor: 3
                <br />
                Region: East China 1
                <br />
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <br />
  <br />
  <div
    class="ant-descriptions css-var-test-id"
  >
    <div
      class="ant-descriptions-header"
    >
      <div
        class="ant-descriptions-title"
      >
        Custom Size
      </div>
      <div
        class="ant-descriptions-extra"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
          type="button"
        >
          <span>
            Edit
          </span>
        </button>
      </div>
    </div>
    <div
      class="ant-descriptions-view"
    >
      <table>
        <tbody>
          <tr
            class="ant-descriptions-row"
          >
            <td
              class="ant-descriptions-item"
              colspan="1"
            >
              <div
                class="ant-descriptions-item-container"
              >
                <span
                  class="ant-descriptions-item-label"
                >
                  Product
                </span>
                <span
                  class="ant-descriptions-item-content"
                >
                  Cloud Database
                </span>
              </div>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <td
              class="ant-descriptions-item"
              colspan="1"
            >
              <div
                class="ant-descriptions-item-container"
              >
                <span
                  class="ant-descriptions-item-label"
                >
                  Billing
                </span>
                <span
                  class="ant-descriptions-item-content"
                >
                  Prepaid
                </span>
              </div>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <td
              class="ant-descriptions-item"
              colspan="1"
            >
              <div
                class="ant-descriptions-item-container"
              >
                <span
                  class="ant-descriptions-item-label"
                >
                  Time
                </span>
                <span
                  class="ant-descriptions-item-content"
                >
                  18:00:00
                </span>
              </div>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <td
              class="ant-descriptions-item"
              colspan="1"
            >
              <div
                class="ant-descriptions-item-container"
              >
                <span
                  class="ant-descriptions-item-label"
                >
                  Amount
                </span>
                <span
                  class="ant-descriptions-item-content"
                >
                  $80.00
                </span>
              </div>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <td
              class="ant-descriptions-item"
              colspan="1"
            >
              <div
                class="ant-descriptions-item-container"
              >
                <span
                  class="ant-descriptions-item-label"
                >
                  Discount
                </span>
                <span
                  class="ant-descriptions-item-content"
                >
                  $20.00
                </span>
              </div>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <td
              class="ant-descriptions-item"
              colspan="1"
            >
              <div
                class="ant-descriptions-item-container"
              >
                <span
                  class="ant-descriptions-item-label"
                >
                  Official
                </span>
                <span
                  class="ant-descriptions-item-content"
                >
                  $60.00
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
`;

exports[`renders components/descriptions/demo/component-token.tsx extend context correctly 2`] = `[]`;

exports[`renders components/descriptions/demo/jsx.tsx extend context correctly 1`] = `
<div
  class="ant-descriptions css-var-test-id"
>
  <div
    class="ant-descriptions-header"
  >
    <div
      class="ant-descriptions-title"
    >
      User Info
    </div>
  </div>
  <div
    class="ant-descriptions-view"
  >
    <table>
      <tbody>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                UserName
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                Zhou Maomao
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Telephone
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                1810000000
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Live
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                Hangzhou, Zhejiang
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Remark
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                empty
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Address
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                No. 18, Wantang Road, Xihu District, Hangzhou, Zhejiang, China
              </span>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`renders components/descriptions/demo/jsx.tsx extend context correctly 2`] = `[]`;

exports[`renders components/descriptions/demo/padding.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-vertical"
  style="gap: 8px;"
>
  <div
    style="width: 600px; border: 1px solid; padding: 20px;"
  >
    <div
      class="ant-descriptions css-var-test-id"
    >
      <div
        class="ant-descriptions-header"
      >
        <div
          class="ant-descriptions-title"
        >
          User Info
        </div>
      </div>
      <div
        class="ant-descriptions-view"
      >
        <table>
          <tbody>
            <tr
              class="ant-descriptions-row"
            >
              <td
                class="ant-descriptions-item"
                colspan="1"
              >
                <div
                  class="ant-descriptions-item-container"
                >
                  <span
                    class="ant-descriptions-item-label"
                  >
                    long
                  </span>
                  <span
                    class="ant-descriptions-item-content"
                  >
                    loooooooooooooooooooooooooooooooooooooooooooooooong
                  </span>
                </div>
              </td>
              <td
                class="ant-descriptions-item"
                colspan="1"
              >
                <div
                  class="ant-descriptions-item-container"
                >
                  <span
                    class="ant-descriptions-item-label"
                  >
                    long
                  </span>
                  <span
                    class="ant-descriptions-item-content"
                  >
                    loooooooooooooooooooooooooooooooooooooooooooooooong
                  </span>
                </div>
              </td>
            </tr>
            <tr
              class="ant-descriptions-row"
            >
              <td
                class="ant-descriptions-item"
                colspan="1"
              >
                <div
                  class="ant-descriptions-item-container"
                >
                  <span
                    class="ant-descriptions-item-label"
                  >
                    long
                  </span>
                  <span
                    class="ant-descriptions-item-content"
                  >
                    loooooooooooooooooooooooooooooooooooooooooooooooong
                  </span>
                </div>
              </td>
              <td
                class="ant-descriptions-item"
                colspan="1"
              >
                <div
                  class="ant-descriptions-item-container"
                >
                  <span
                    class="ant-descriptions-item-label"
                  >
                    long
                  </span>
                  <span
                    class="ant-descriptions-item-content"
                  >
                    loooooooooooooooooooooooooooooooooooooooooooooooong
                  </span>
                </div>
              </td>
            </tr>
            <tr
              class="ant-descriptions-row"
            >
              <td
                class="ant-descriptions-item"
                colspan="2"
              >
                <div
                  class="ant-descriptions-item-container"
                >
                  <span
                    class="ant-descriptions-item-label"
                  >
                    long
                  </span>
                  <span
                    class="ant-descriptions-item-content"
                  >
                    loooooooooooooooooooooooooooooooooooooooooooooooong
                  </span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <div
    style="width: 600px; border: 1px solid; padding: 20px;"
  >
    <div
      class="ant-descriptions css-var-test-id"
    >
      <div
        class="ant-descriptions-header"
      >
        <div
          class="ant-descriptions-title"
        >
          User Info
        </div>
      </div>
      <div
        class="ant-descriptions-view"
      >
        <table>
          <tbody>
            <tr
              class="ant-descriptions-row"
            >
              <th
                class="ant-descriptions-item"
                colspan="1"
              >
                <div
                  class="ant-descriptions-item-container"
                >
                  <span
                    class="ant-descriptions-item-label"
                  >
                    long
                  </span>
                </div>
              </th>
              <th
                class="ant-descriptions-item"
                colspan="1"
              >
                <div
                  class="ant-descriptions-item-container"
                >
                  <span
                    class="ant-descriptions-item-label"
                  >
                    long
                  </span>
                </div>
              </th>
            </tr>
            <tr
              class="ant-descriptions-row"
            >
              <td
                class="ant-descriptions-item"
                colspan="1"
              >
                <div
                  class="ant-descriptions-item-container"
                >
                  <span
                    class="ant-descriptions-item-content"
                  >
                    loooooooooooooooooooooooooooooooooooooooooooooooong
                  </span>
                </div>
              </td>
              <td
                class="ant-descriptions-item"
                colspan="1"
              >
                <div
                  class="ant-descriptions-item-container"
                >
                  <span
                    class="ant-descriptions-item-content"
                  >
                    loooooooooooooooooooooooooooooooooooooooooooooooong
                  </span>
                </div>
              </td>
            </tr>
            <tr
              class="ant-descriptions-row"
            >
              <th
                class="ant-descriptions-item"
                colspan="1"
              >
                <div
                  class="ant-descriptions-item-container"
                >
                  <span
                    class="ant-descriptions-item-label"
                  >
                    long
                  </span>
                </div>
              </th>
              <th
                class="ant-descriptions-item"
                colspan="1"
              >
                <div
                  class="ant-descriptions-item-container"
                >
                  <span
                    class="ant-descriptions-item-label"
                  >
                    long
                  </span>
                </div>
              </th>
            </tr>
            <tr
              class="ant-descriptions-row"
            >
              <td
                class="ant-descriptions-item"
                colspan="1"
              >
                <div
                  class="ant-descriptions-item-container"
                >
                  <span
                    class="ant-descriptions-item-content"
                  >
                    loooooooooooooooooooooooooooooooooooooooooooooooong
                  </span>
                </div>
              </td>
              <td
                class="ant-descriptions-item"
                colspan="1"
              >
                <div
                  class="ant-descriptions-item-container"
                >
                  <span
                    class="ant-descriptions-item-content"
                  >
                    loooooooooooooooooooooooooooooooooooooooooooooooong
                  </span>
                </div>
              </td>
            </tr>
            <tr
              class="ant-descriptions-row"
            >
              <th
                class="ant-descriptions-item"
                colspan="2"
              >
                <div
                  class="ant-descriptions-item-container"
                >
                  <span
                    class="ant-descriptions-item-label"
                  >
                    long
                  </span>
                </div>
              </th>
            </tr>
            <tr
              class="ant-descriptions-row"
            >
              <td
                class="ant-descriptions-item"
                colspan="2"
              >
                <div
                  class="ant-descriptions-item-container"
                >
                  <span
                    class="ant-descriptions-item-content"
                  >
                    loooooooooooooooooooooooooooooooooooooooooooooooong
                  </span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/descriptions/demo/padding.tsx extend context correctly 2`] = `[]`;

exports[`renders components/descriptions/demo/responsive.tsx extend context correctly 1`] = `
<div
  class="ant-descriptions ant-descriptions-bordered css-var-test-id"
>
  <div
    class="ant-descriptions-header"
  >
    <div
      class="ant-descriptions-title"
    >
      Responsive Descriptions
    </div>
  </div>
  <div
    class="ant-descriptions-view"
  >
    <table>
      <tbody>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Product
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              Cloud Database
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Billing
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              Prepaid
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Time
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              18:00:00
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Amount
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              $80.00
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Discount
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              $20.00
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Official
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              $60.00
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Config Info
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              Data disk type: MongoDB
              <br />
              Database version: 3.4
              <br />
              Package: dds.mongo.mid
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Hardware Info
            </span>
          </th>
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              CPU: 6 Core 3.5 GHz
              <br />
              Storage space: 10 GB
              <br />
              Replication factor: 3
              <br />
              Region: East China 1
            </span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`renders components/descriptions/demo/responsive.tsx extend context correctly 2`] = `[]`;

exports[`renders components/descriptions/demo/size.tsx extend context correctly 1`] = `
<div>
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-wrapper ant-radio-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-checked"
      >
        <input
          checked=""
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="default"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        default
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="middle"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        middle
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="small"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        small
      </span>
    </label>
  </div>
  <br />
  <br />
  <div
    class="ant-descriptions ant-descriptions-bordered css-var-test-id"
  >
    <div
      class="ant-descriptions-header"
    >
      <div
        class="ant-descriptions-title"
      >
        Custom Size
      </div>
      <div
        class="ant-descriptions-extra"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
          type="button"
        >
          <span>
            Edit
          </span>
        </button>
      </div>
    </div>
    <div
      class="ant-descriptions-view"
    >
      <table>
        <tbody>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
            >
              <span>
                Product
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
            >
              <span>
                Cloud Database
              </span>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
            >
              <span>
                Billing
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
            >
              <span>
                Prepaid
              </span>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
            >
              <span>
                Time
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
            >
              <span>
                18:00:00
              </span>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
            >
              <span>
                Amount
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
            >
              <span>
                $80.00
              </span>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
            >
              <span>
                Discount
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
            >
              <span>
                $20.00
              </span>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
            >
              <span>
                Official
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
            >
              <span>
                $60.00
              </span>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
            >
              <span>
                Config Info
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
            >
              <span>
                Data disk type: MongoDB
                <br />
                Database version: 3.4
                <br />
                Package: dds.mongo.mid
                <br />
                Storage space: 10 GB
                <br />
                Replication factor: 3
                <br />
                Region: East China 1
                <br />
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <br />
  <br />
  <div
    class="ant-descriptions css-var-test-id"
  >
    <div
      class="ant-descriptions-header"
    >
      <div
        class="ant-descriptions-title"
      >
        Custom Size
      </div>
      <div
        class="ant-descriptions-extra"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
          type="button"
        >
          <span>
            Edit
          </span>
        </button>
      </div>
    </div>
    <div
      class="ant-descriptions-view"
    >
      <table>
        <tbody>
          <tr
            class="ant-descriptions-row"
          >
            <td
              class="ant-descriptions-item"
              colspan="1"
            >
              <div
                class="ant-descriptions-item-container"
              >
                <span
                  class="ant-descriptions-item-label"
                >
                  Product
                </span>
                <span
                  class="ant-descriptions-item-content"
                >
                  Cloud Database
                </span>
              </div>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <td
              class="ant-descriptions-item"
              colspan="1"
            >
              <div
                class="ant-descriptions-item-container"
              >
                <span
                  class="ant-descriptions-item-label"
                >
                  Billing
                </span>
                <span
                  class="ant-descriptions-item-content"
                >
                  Prepaid
                </span>
              </div>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <td
              class="ant-descriptions-item"
              colspan="1"
            >
              <div
                class="ant-descriptions-item-container"
              >
                <span
                  class="ant-descriptions-item-label"
                >
                  Time
                </span>
                <span
                  class="ant-descriptions-item-content"
                >
                  18:00:00
                </span>
              </div>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <td
              class="ant-descriptions-item"
              colspan="1"
            >
              <div
                class="ant-descriptions-item-container"
              >
                <span
                  class="ant-descriptions-item-label"
                >
                  Amount
                </span>
                <span
                  class="ant-descriptions-item-content"
                >
                  $80.00
                </span>
              </div>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <td
              class="ant-descriptions-item"
              colspan="1"
            >
              <div
                class="ant-descriptions-item-container"
              >
                <span
                  class="ant-descriptions-item-label"
                >
                  Discount
                </span>
                <span
                  class="ant-descriptions-item-content"
                >
                  $20.00
                </span>
              </div>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <td
              class="ant-descriptions-item"
              colspan="1"
            >
              <div
                class="ant-descriptions-item-container"
              >
                <span
                  class="ant-descriptions-item-label"
                >
                  Official
                </span>
                <span
                  class="ant-descriptions-item-content"
                >
                  $60.00
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
`;

exports[`renders components/descriptions/demo/size.tsx extend context correctly 2`] = `[]`;

exports[`renders components/descriptions/demo/style.tsx extend context correctly 1`] = `
Array [
  <button
    aria-checked="true"
    class="ant-switch css-var-test-id ant-switch-checked"
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      >
        Border
      </span>
      <span
        class="ant-switch-inner-unchecked"
      >
        No Border
      </span>
    </span>
  </button>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
  />,
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-wrapper ant-radio-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-checked"
      >
        <input
          checked=""
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="horizontal"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        horizontal
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="vertical"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        vertical
      </span>
    </label>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
  />,
  <div
    class="ant-descriptions ant-descriptions-bordered css-var-test-id"
  >
    <div
      class="ant-descriptions-header"
    >
      <div
        class="ant-descriptions-title"
      >
        User Info
      </div>
    </div>
    <div
      class="ant-descriptions-view"
    >
      <table>
        <tbody>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
              style="background: red;"
            >
              <span>
                Product
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
              style="background: green;"
            >
              <span>
                Cloud Database
              </span>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
            >
              <span>
                Billing Mode
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
            >
              <span>
                Prepaid
              </span>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
            >
              <span>
                Automatic Renewal
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
            >
              <span>
                YES
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
  />,
  <div
    class="ant-descriptions ant-descriptions-bordered css-var-test-id"
  >
    <div
      class="ant-descriptions-header"
    >
      <div
        class="ant-descriptions-title"
      >
        Root style
      </div>
    </div>
    <div
      class="ant-descriptions-view"
    >
      <table>
        <tbody>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
              style="background: red;"
            >
              <span>
                Product
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
              style="background: green;"
            >
              <span>
                Cloud Database
              </span>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
              style="background: red;"
            >
              <span>
                Billing Mode
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
              style="background: green;"
            >
              <span>
                Prepaid
              </span>
            </td>
          </tr>
          <tr
            class="ant-descriptions-row"
          >
            <th
              class="ant-descriptions-item-label"
              colspan="1"
              style="background: red; color: orange;"
            >
              <span>
                Automatic Renewal
              </span>
            </th>
            <td
              class="ant-descriptions-item-content"
              colspan="1"
              style="background: green; color: blue;"
            >
              <span>
                YES
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>,
]
`;

exports[`renders components/descriptions/demo/style.tsx extend context correctly 2`] = `[]`;

exports[`renders components/descriptions/demo/text.tsx extend context correctly 1`] = `
<div
  class="ant-descriptions css-var-test-id"
>
  <div
    class="ant-descriptions-header"
  >
    <div
      class="ant-descriptions-title"
    >
      User Info
    </div>
  </div>
  <div
    class="ant-descriptions-view"
  >
    <table>
      <tbody>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Product
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                Cloud Database
              </span>
            </div>
          </td>
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                <div
                  style="display: flex;"
                >
                  Billing Mode
                </div>
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                Prepaid
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Automatic Renewal
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                YES
              </span>
            </div>
          </td>
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Order time
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                2018-04-24 18:00:00
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="2"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Usage Time
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                2019-04-24 18:00:00
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="2"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Status
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                <span
                  class="ant-badge ant-badge-status ant-badge-not-a-wrapper css-var-test-id"
                >
                  <span
                    class="ant-badge-status-dot ant-badge-status-processing"
                  />
                  <span
                    class="ant-badge-status-text"
                  >
                    Running
                  </span>
                </span>
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Negotiated Amount
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                $80.00
              </span>
            </div>
          </td>
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Discount
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                $20.00
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Official Receipts
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                $60.00
              </span>
            </div>
          </td>
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Config Info
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                Data disk type: MongoDB
                <br />
                Database version: 3.4
                <br />
                Package: dds.mongo.mid
                <br />
                Storage space: 10 GB
                <br />
                Replication factor: 3
                <br />
                Region: East China 1
                <br />
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Official Receipts
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                $60.00
              </span>
            </div>
          </td>
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Config Info
              </span>
              <span
                class="ant-descriptions-item-content"
              >
                <div
                  class="css-var-test-id ant-table-css-var ant-table-wrapper"
                >
                  <div
                    class="ant-spin-nested-loading css-var-test-id"
                  >
                    <div
                      class="ant-spin-container"
                    >
                      <div
                        class="ant-table ant-table-small css-var-test-id ant-table-css-var"
                      >
                        <div
                          class="ant-table-container"
                        >
                          <div
                            class="ant-table-content"
                          >
                            <table
                              style="table-layout: auto;"
                            >
                              <thead
                                class="ant-table-thead"
                              >
                                <tr>
                                  <th
                                    class="ant-table-cell"
                                    scope="col"
                                  >
                                    姓名
                                  </th>
                                  <th
                                    class="ant-table-cell"
                                    scope="col"
                                  >
                                    年龄
                                  </th>
                                  <th
                                    class="ant-table-cell"
                                    scope="col"
                                  >
                                    住址
                                  </th>
                                </tr>
                              </thead>
                              <tbody
                                class="ant-table-tbody"
                              >
                                <tr
                                  class="ant-table-row ant-table-row-level-0"
                                  data-row-key="1"
                                >
                                  <td
                                    class="ant-table-cell"
                                  >
                                    胡彦斌
                                  </td>
                                  <td
                                    class="ant-table-cell"
                                  >
                                    32
                                  </td>
                                  <td
                                    class="ant-table-cell"
                                  >
                                    西湖区湖底公园1号
                                  </td>
                                </tr>
                                <tr
                                  class="ant-table-row ant-table-row-level-0"
                                  data-row-key="2"
                                >
                                  <td
                                    class="ant-table-cell"
                                  >
                                    胡彦祖
                                  </td>
                                  <td
                                    class="ant-table-cell"
                                  >
                                    42
                                  </td>
                                  <td
                                    class="ant-table-cell"
                                  >
                                    西湖区湖底公园1号
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`renders components/descriptions/demo/text.tsx extend context correctly 2`] = `
[
  "Warning: [antd: Descriptions] Sum of column \`span\` in a line not match \`column\` of Descriptions.",
]
`;

exports[`renders components/descriptions/demo/vertical.tsx extend context correctly 1`] = `
<div
  class="ant-descriptions css-var-test-id"
>
  <div
    class="ant-descriptions-header"
  >
    <div
      class="ant-descriptions-title"
    >
      User Info
    </div>
  </div>
  <div
    class="ant-descriptions-view"
  >
    <table>
      <tbody>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                UserName
              </span>
            </div>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-content"
              >
                Zhou Maomao
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Telephone
              </span>
            </div>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-content"
              >
                1810000000
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Live
              </span>
            </div>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-content"
              >
                Hangzhou, Zhejiang
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Address
              </span>
            </div>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-content"
              >
                No. 18, Wantang Road, Xihu District, Hangzhou, Zhejiang, China
              </span>
            </div>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-label"
              >
                Remark
              </span>
            </div>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item"
            colspan="1"
          >
            <div
              class="ant-descriptions-item-container"
            >
              <span
                class="ant-descriptions-item-content"
              >
                empty
              </span>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`renders components/descriptions/demo/vertical.tsx extend context correctly 2`] = `
[
  "Warning: [antd: Descriptions] Sum of column \`span\` in a line not match \`column\` of Descriptions.",
]
`;

exports[`renders components/descriptions/demo/vertical-border.tsx extend context correctly 1`] = `
<div
  class="ant-descriptions ant-descriptions-bordered css-var-test-id"
>
  <div
    class="ant-descriptions-header"
  >
    <div
      class="ant-descriptions-title"
    >
      User Info
    </div>
  </div>
  <div
    class="ant-descriptions-view"
  >
    <table>
      <tbody>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Product
            </span>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              Cloud Database
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Billing Mode
            </span>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              Prepaid
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Automatic Renewal
            </span>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              YES
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Order time
            </span>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              2018-04-24 18:00:00
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Usage Time
            </span>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              2019-04-24 18:00:00
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Status
            </span>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              <span
                class="ant-badge ant-badge-status ant-badge-not-a-wrapper css-var-test-id"
              >
                <span
                  class="ant-badge-status-dot ant-badge-status-processing"
                />
                <span
                  class="ant-badge-status-text"
                >
                  Running
                </span>
              </span>
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Negotiated Amount
            </span>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              $80.00
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Discount
            </span>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              $20.00
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Official Receipts
            </span>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              $60.00
            </span>
          </td>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <th
            class="ant-descriptions-item-label"
            colspan="1"
          >
            <span>
              Config Info
            </span>
          </th>
        </tr>
        <tr
          class="ant-descriptions-row"
        >
          <td
            class="ant-descriptions-item-content"
            colspan="1"
          >
            <span>
              Data disk type: MongoDB
              <br />
              Database version: 3.4
              <br />
              Package: dds.mongo.mid
              <br />
              Storage space: 10 GB
              <br />
              Replication factor: 3
              <br />
              Region: East China 1
              <br />
            </span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
`;

exports[`renders components/descriptions/demo/vertical-border.tsx extend context correctly 2`] = `
[
  "Warning: [antd: Descriptions] Sum of column \`span\` in a line not match \`column\` of Descriptions.",
]
`;
