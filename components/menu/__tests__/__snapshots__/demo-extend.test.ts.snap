// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/menu/demo/component-token.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <ul
      class="ant-menu-overflow ant-menu ant-menu-root ant-menu-horizontal ant-menu-light css-var-test-id ant-menu-css-var"
      data-menu-list="true"
      role="menu"
      tabindex="0"
    >
      <li
        aria-describedby="test-id"
        class="ant-menu-overflow-item ant-menu-item ant-menu-item-selected"
        data-menu-id="rc-menu-uuid-mail"
        role="menuitem"
        style="opacity: 1; order: 0;"
        tabindex="-1"
      >
        <span
          aria-label="mail"
          class="anticon anticon-mail ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="mail"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation One
        </span>
      </li>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; top: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          />
        </div>
      </div>
      <li
        aria-describedby="test-id"
        aria-disabled="true"
        class="ant-menu-overflow-item ant-menu-item ant-menu-item-disabled"
        role="menuitem"
        style="opacity: 1; order: 1;"
      >
        <span
          aria-label="appstore"
          class="anticon anticon-appstore ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="appstore"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Two
        </span>
      </li>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; top: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          />
        </div>
      </div>
      <li
        class="ant-menu-overflow-item ant-menu-submenu ant-menu-submenu-horizontal"
        role="none"
        style="opacity: 1; order: 2;"
      >
        <div
          aria-controls="rc-menu-uuid-SubMenu-popup"
          aria-expanded="false"
          aria-haspopup="true"
          class="ant-menu-submenu-title"
          role="menuitem"
          tabindex="-1"
        >
          <span
            aria-label="setting"
            class="anticon anticon-setting ant-menu-item-icon"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="setting"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
              />
            </svg>
          </span>
          <span
            class="ant-menu-title-content"
          >
            Navigation Three - Submenu
          </span>
          <i
            class="ant-menu-submenu-arrow"
          />
        </div>
      </li>
      <li
        aria-describedby="test-id"
        class="ant-menu-overflow-item ant-menu-item ant-menu-item-only-child"
        role="menuitem"
        style="opacity: 1; order: 3;"
        tabindex="-1"
      >
        <span
          class="ant-menu-title-content"
        >
          <a
            href="https://ant.design"
            rel="noopener noreferrer"
            target="_blank"
          >
            Navigation Four - Link
          </a>
        </span>
      </li>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; top: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          />
        </div>
      </div>
      <li
        aria-hidden="true"
        class="ant-menu-overflow-item ant-menu-overflow-item-rest ant-menu-submenu ant-menu-submenu-horizontal"
        role="none"
        style="opacity: 0; height: 0px; overflow-y: hidden; order: 9007199254740991; pointer-events: none; position: absolute;"
      >
        <div
          aria-controls="rc-menu-uuid-rc-menu-more-popup"
          aria-expanded="false"
          aria-haspopup="true"
          class="ant-menu-submenu-title"
          data-menu-id="rc-menu-uuid-rc-menu-more"
          role="menuitem"
          tabindex="-1"
        >
          <span
            aria-label="ellipsis"
            class="anticon anticon-ellipsis"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="ellipsis"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
              />
            </svg>
          </span>
          <i
            class="ant-menu-submenu-arrow"
          />
        </div>
        <div
          class="ant-menu-submenu ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var ant-menu-submenu-placement-bottomLeft"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <ul
            class="ant-menu ant-menu-sub ant-menu-vertical"
            data-menu-list="true"
            id="rc-menu-uuid-rc-menu-more-popup"
            role="menu"
          />
        </div>
      </li>
    </ul>
    <div
      aria-hidden="true"
      style="display: none;"
    />
    <ul
      class="ant-menu ant-menu-root ant-menu-vertical ant-menu-dark ant-menu-inline-collapsed css-var-test-id ant-menu-css-var"
      data-menu-list="true"
      role="menu"
      style="width: 56px;"
      tabindex="0"
    >
      <li
        aria-describedby="test-id"
        class="ant-menu-item ant-menu-item-selected"
        data-menu-id="rc-menu-uuid-1"
        role="menuitem"
        tabindex="-1"
      >
        <span
          aria-label="pie-chart"
          class="anticon anticon-pie-chart ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="pie-chart"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M864 518H506V160c0-4.4-3.6-8-8-8h-26a398.46 398.46 0 00-282.8 117.1 398.19 398.19 0 00-85.7 127.1A397.61 397.61 0 0072 552a398.46 398.46 0 00117.1 282.8c36.7 36.7 79.5 65.6 127.1 85.7A397.61 397.61 0 00472 952a398.46 398.46 0 00282.8-117.1c36.7-36.7 65.6-79.5 85.7-127.1A397.61 397.61 0 00872 552v-26c0-4.4-3.6-8-8-8zM705.7 787.8A331.59 331.59 0 01470.4 884c-88.1-.4-170.9-34.9-233.2-97.2C174.5 724.1 140 640.7 140 552c0-88.7 34.5-172.1 97.2-234.8 54.6-54.6 124.9-87.9 200.8-95.5V586h364.3c-7.7 76.3-41.3 147-96.6 201.8zM952 462.4l-2.6-28.2c-8.5-92.1-49.4-179-115.2-244.6A399.4 399.4 0 00589 74.6L560.7 72c-4.7-.4-8.7 3.2-8.7 7.9V464c0 4.4 3.6 8 8 8l384-1c4.7 0 8.4-4 8-8.6zm-332.2-58.2V147.6a332.24 332.24 0 01166.4 89.8c45.7 45.6 77 103.6 90 166.1l-256.4.7z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Option 1
        </span>
      </li>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; top: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          >
            Option 1
          </div>
        </div>
      </div>
      <li
        aria-describedby="test-id"
        class="ant-menu-item"
        data-menu-id="rc-menu-uuid-2"
        role="menuitem"
        tabindex="-1"
      >
        <span
          aria-label="desktop"
          class="anticon anticon-desktop ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="desktop"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M928 140H96c-17.7 0-32 14.3-32 32v496c0 17.7 14.3 32 32 32h380v112H304c-8.8 0-16 7.2-16 16v48c0 4.4 3.6 8 8 8h432c4.4 0 8-3.6 8-8v-48c0-8.8-7.2-16-16-16H548V700h380c17.7 0 32-14.3 32-32V172c0-17.7-14.3-32-32-32zm-40 488H136V212h752v416z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Option 2
        </span>
      </li>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; top: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          >
            Option 2
          </div>
        </div>
      </div>
      <li
        aria-describedby="test-id"
        class="ant-menu-item"
        data-menu-id="rc-menu-uuid-3"
        role="menuitem"
        tabindex="-1"
      >
        <span
          aria-label="container"
          class="anticon anticon-container ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="container"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-40 824H232V687h97.9c11.6 32.8 32 62.3 59.1 84.7 34.5 28.5 78.2 44.3 123 44.3s88.5-15.7 123-44.3c27.1-22.4 47.5-51.9 59.1-84.7H792v-63H643.6l-5.2 24.7C626.4 708.5 573.2 752 512 752s-114.4-43.5-126.5-103.3l-5.2-24.7H232V136h560v752zM320 341h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zm0 160h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Option 3
        </span>
      </li>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; top: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          >
            Option 3
          </div>
        </div>
      </div>
      <li
        class="ant-menu-submenu ant-menu-submenu-vertical ant-menu-submenu-open"
        role="none"
      >
        <div
          aria-controls="rc-menu-uuid-sub1-popup"
          aria-expanded="true"
          aria-haspopup="true"
          class="ant-menu-submenu-title"
          data-menu-id="rc-menu-uuid-sub1"
          role="menuitem"
          tabindex="-1"
        >
          <span
            aria-label="mail"
            class="anticon anticon-mail ant-menu-item-icon"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="mail"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"
              />
            </svg>
          </span>
          <span
            class="ant-menu-title-content"
          >
            Navigation One
          </span>
          <i
            class="ant-menu-submenu-arrow"
          />
        </div>
        <div
          class="ant-menu-submenu ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var ant-menu-submenu-placement-rightTop"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <ul
            class="ant-menu ant-menu-sub ant-menu-vertical"
            data-menu-list="true"
            id="rc-menu-uuid-sub1-popup"
            role="menu"
          >
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-5"
              role="menuitem"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 5
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-6"
              role="menuitem"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 6
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-7"
              role="menuitem"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 7
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-8"
              role="menuitem"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 8
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
          </ul>
        </div>
      </li>
      <li
        class="ant-menu-submenu ant-menu-submenu-vertical"
        role="none"
      >
        <div
          aria-controls="rc-menu-uuid-sub2-popup"
          aria-expanded="false"
          aria-haspopup="true"
          class="ant-menu-submenu-title"
          data-menu-id="rc-menu-uuid-sub2"
          role="menuitem"
          tabindex="-1"
        >
          <span
            aria-label="appstore"
            class="anticon anticon-appstore ant-menu-item-icon"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="appstore"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"
              />
            </svg>
          </span>
          <span
            class="ant-menu-title-content"
          >
            Navigation Two
          </span>
          <i
            class="ant-menu-submenu-arrow"
          />
        </div>
        <div
          class="ant-menu-submenu ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var ant-menu-submenu-placement-rightTop"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <ul
            class="ant-menu ant-menu-sub ant-menu-vertical"
            data-menu-list="true"
            id="rc-menu-uuid-sub2-popup"
            role="menu"
          >
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-9"
              role="menuitem"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 9
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-10"
              role="menuitem"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 10
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
            <li
              class="ant-menu-submenu ant-menu-submenu-vertical"
              role="none"
            >
              <div
                aria-controls="rc-menu-uuid-sub3-popup"
                aria-expanded="false"
                aria-haspopup="true"
                class="ant-menu-submenu-title"
                data-menu-id="rc-menu-uuid-sub3"
                role="menuitem"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Submenu
                </span>
                <i
                  class="ant-menu-submenu-arrow"
                />
              </div>
              <div
                class="ant-menu-submenu ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var ant-menu-submenu-placement-rightTop"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <ul
                  class="ant-menu ant-menu-sub ant-menu-vertical"
                  data-menu-list="true"
                  id="rc-menu-uuid-sub3-popup"
                  role="menu"
                >
                  <li
                    aria-describedby="test-id"
                    class="ant-menu-item ant-menu-item-only-child"
                    data-menu-id="rc-menu-uuid-11"
                    role="menuitem"
                    tabindex="-1"
                  >
                    <span
                      class="ant-menu-title-content"
                    >
                      Option 11
                    </span>
                  </li>
                  <div
                    class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                  >
                    <div
                      class="ant-tooltip-arrow"
                      style="position: absolute; top: 0px; left: 0px;"
                    />
                    <div
                      class="ant-tooltip-content"
                    >
                      <div
                        class="ant-tooltip-inner"
                        id="test-id"
                        role="tooltip"
                      />
                    </div>
                  </div>
                  <li
                    aria-describedby="test-id"
                    class="ant-menu-item ant-menu-item-only-child"
                    data-menu-id="rc-menu-uuid-12"
                    role="menuitem"
                    tabindex="-1"
                  >
                    <span
                      class="ant-menu-title-content"
                    >
                      Option 12
                    </span>
                  </li>
                  <div
                    class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                  >
                    <div
                      class="ant-tooltip-arrow"
                      style="position: absolute; top: 0px; left: 0px;"
                    />
                    <div
                      class="ant-tooltip-content"
                    >
                      <div
                        class="ant-tooltip-inner"
                        id="test-id"
                        role="tooltip"
                      />
                    </div>
                  </div>
                </ul>
              </div>
            </li>
          </ul>
        </div>
      </li>
    </ul>
    <div
      aria-hidden="true"
      style="display: none;"
    />
  </div>
  <div
    class="ant-space-item"
  >
    <ul
      class="ant-menu-overflow ant-menu ant-menu-root ant-menu-horizontal ant-menu-light css-var-test-id ant-menu-css-var"
      data-menu-list="true"
      role="menu"
      tabindex="0"
    >
      <li
        aria-describedby="test-id"
        class="ant-menu-overflow-item ant-menu-item ant-menu-item-selected"
        data-menu-id="rc-menu-uuid-mail"
        role="menuitem"
        style="opacity: 1; order: 0;"
        tabindex="-1"
      >
        <span
          aria-label="mail"
          class="anticon anticon-mail ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="mail"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation One
        </span>
      </li>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; top: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          />
        </div>
      </div>
      <li
        aria-describedby="test-id"
        aria-disabled="true"
        class="ant-menu-overflow-item ant-menu-item ant-menu-item-disabled"
        role="menuitem"
        style="opacity: 1; order: 1;"
      >
        <span
          aria-label="appstore"
          class="anticon anticon-appstore ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="appstore"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Two
        </span>
      </li>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; top: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          />
        </div>
      </div>
      <li
        class="ant-menu-overflow-item ant-menu-submenu ant-menu-submenu-horizontal"
        role="none"
        style="opacity: 1; order: 2;"
      >
        <div
          aria-controls="rc-menu-uuid-SubMenu-popup"
          aria-expanded="false"
          aria-haspopup="true"
          class="ant-menu-submenu-title"
          role="menuitem"
          tabindex="-1"
        >
          <span
            aria-label="setting"
            class="anticon anticon-setting ant-menu-item-icon"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="setting"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
              />
            </svg>
          </span>
          <span
            class="ant-menu-title-content"
          >
            Navigation Three - Submenu
          </span>
          <i
            class="ant-menu-submenu-arrow"
          />
        </div>
      </li>
      <li
        aria-describedby="test-id"
        class="ant-menu-overflow-item ant-menu-item ant-menu-item-only-child"
        role="menuitem"
        style="opacity: 1; order: 3;"
        tabindex="-1"
      >
        <span
          class="ant-menu-title-content"
        >
          <a
            href="https://ant.design"
            rel="noopener noreferrer"
            target="_blank"
          >
            Navigation Four - Link
          </a>
        </span>
      </li>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; top: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          />
        </div>
      </div>
      <li
        aria-hidden="true"
        class="ant-menu-overflow-item ant-menu-overflow-item-rest ant-menu-submenu ant-menu-submenu-horizontal"
        role="none"
        style="opacity: 0; height: 0px; overflow-y: hidden; order: 9007199254740991; pointer-events: none; position: absolute;"
      >
        <div
          aria-controls="rc-menu-uuid-rc-menu-more-popup"
          aria-expanded="false"
          aria-haspopup="true"
          class="ant-menu-submenu-title"
          data-menu-id="rc-menu-uuid-rc-menu-more"
          role="menuitem"
          tabindex="-1"
        >
          <span
            aria-label="ellipsis"
            class="anticon anticon-ellipsis"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="ellipsis"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
              />
            </svg>
          </span>
          <i
            class="ant-menu-submenu-arrow"
          />
        </div>
        <div
          class="ant-menu-submenu ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var ant-menu-submenu-placement-bottomLeft"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <ul
            class="ant-menu ant-menu-sub ant-menu-vertical"
            data-menu-list="true"
            id="rc-menu-uuid-rc-menu-more-popup"
            role="menu"
          />
        </div>
      </li>
    </ul>
    <div
      aria-hidden="true"
      style="display: none;"
    />
  </div>
  <div
    class="ant-space-item"
  >
    <ul
      class="ant-menu ant-menu-root ant-menu-inline ant-menu-dark css-var-test-id ant-menu-css-var"
      data-menu-list="true"
      role="menu"
      style="width: 256px;"
      tabindex="0"
    >
      <li
        aria-describedby="test-id"
        class="ant-menu-item ant-menu-item-selected"
        data-menu-id="rc-menu-uuid-1"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="pie-chart"
          class="anticon anticon-pie-chart ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="pie-chart"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M864 518H506V160c0-4.4-3.6-8-8-8h-26a398.46 398.46 0 00-282.8 117.1 398.19 398.19 0 00-85.7 127.1A397.61 397.61 0 0072 552a398.46 398.46 0 00117.1 282.8c36.7 36.7 79.5 65.6 127.1 85.7A397.61 397.61 0 00472 952a398.46 398.46 0 00282.8-117.1c36.7-36.7 65.6-79.5 85.7-127.1A397.61 397.61 0 00872 552v-26c0-4.4-3.6-8-8-8zM705.7 787.8A331.59 331.59 0 01470.4 884c-88.1-.4-170.9-34.9-233.2-97.2C174.5 724.1 140 640.7 140 552c0-88.7 34.5-172.1 97.2-234.8 54.6-54.6 124.9-87.9 200.8-95.5V586h364.3c-7.7 76.3-41.3 147-96.6 201.8zM952 462.4l-2.6-28.2c-8.5-92.1-49.4-179-115.2-244.6A399.4 399.4 0 00589 74.6L560.7 72c-4.7-.4-8.7 3.2-8.7 7.9V464c0 4.4 3.6 8 8 8l384-1c4.7 0 8.4-4 8-8.6zm-332.2-58.2V147.6a332.24 332.24 0 01166.4 89.8c45.7 45.6 77 103.6 90 166.1l-256.4.7z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Option 1
        </span>
      </li>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; top: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          />
        </div>
      </div>
      <li
        aria-describedby="test-id"
        class="ant-menu-item"
        data-menu-id="rc-menu-uuid-2"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="desktop"
          class="anticon anticon-desktop ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="desktop"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M928 140H96c-17.7 0-32 14.3-32 32v496c0 17.7 14.3 32 32 32h380v112H304c-8.8 0-16 7.2-16 16v48c0 4.4 3.6 8 8 8h432c4.4 0 8-3.6 8-8v-48c0-8.8-7.2-16-16-16H548V700h380c17.7 0 32-14.3 32-32V172c0-17.7-14.3-32-32-32zm-40 488H136V212h752v416z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Option 2
        </span>
      </li>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; top: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          />
        </div>
      </div>
      <li
        aria-describedby="test-id"
        class="ant-menu-item"
        data-menu-id="rc-menu-uuid-3"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="container"
          class="anticon anticon-container ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="container"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-40 824H232V687h97.9c11.6 32.8 32 62.3 59.1 84.7 34.5 28.5 78.2 44.3 123 44.3s88.5-15.7 123-44.3c27.1-22.4 47.5-51.9 59.1-84.7H792v-63H643.6l-5.2 24.7C626.4 708.5 573.2 752 512 752s-114.4-43.5-126.5-103.3l-5.2-24.7H232V136h560v752zM320 341h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zm0 160h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Option 3
        </span>
      </li>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; top: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          />
        </div>
      </div>
      <li
        class="ant-menu-submenu ant-menu-submenu-inline ant-menu-submenu-open"
        role="none"
      >
        <div
          aria-controls="rc-menu-uuid-sub1-popup"
          aria-expanded="true"
          aria-haspopup="true"
          class="ant-menu-submenu-title"
          data-menu-id="rc-menu-uuid-sub1"
          role="menuitem"
          style="padding-left: 24px;"
          tabindex="-1"
        >
          <span
            aria-label="mail"
            class="anticon anticon-mail ant-menu-item-icon"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="mail"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"
              />
            </svg>
          </span>
          <span
            class="ant-menu-title-content"
          >
            Navigation One
          </span>
          <i
            class="ant-menu-submenu-arrow"
          />
        </div>
        <div
          class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <ul
            class="ant-menu ant-menu-sub ant-menu-inline"
            data-menu-list="true"
            id="rc-menu-uuid-sub1-popup"
            role="menu"
          >
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-5"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 5
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-6"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 6
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-7"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 7
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-8"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 8
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
          </ul>
        </div>
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-sub1-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-5"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 5
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-6"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 6
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-7"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 7
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-8"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 8
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
        </ul>
      </li>
      <li
        class="ant-menu-submenu ant-menu-submenu-inline"
        role="none"
      >
        <div
          aria-controls="rc-menu-uuid-sub2-popup"
          aria-expanded="false"
          aria-haspopup="true"
          class="ant-menu-submenu-title"
          data-menu-id="rc-menu-uuid-sub2"
          role="menuitem"
          style="padding-left: 24px;"
          tabindex="-1"
        >
          <span
            aria-label="appstore"
            class="anticon anticon-appstore ant-menu-item-icon"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="appstore"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"
              />
            </svg>
          </span>
          <span
            class="ant-menu-title-content"
          >
            Navigation Two
          </span>
          <i
            class="ant-menu-submenu-arrow"
          />
        </div>
        <div
          class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <ul
            class="ant-menu ant-menu-sub ant-menu-inline"
            data-menu-list="true"
            id="rc-menu-uuid-sub2-popup"
            role="menu"
          >
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-9"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 9
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-10"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 10
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
            <li
              class="ant-menu-submenu ant-menu-submenu-inline"
              role="none"
            >
              <div
                aria-controls="rc-menu-uuid-sub3-popup"
                aria-expanded="false"
                aria-haspopup="true"
                class="ant-menu-submenu-title"
                data-menu-id="rc-menu-uuid-sub3"
                role="menuitem"
                style="padding-left: 48px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Submenu
                </span>
                <i
                  class="ant-menu-submenu-arrow"
                />
              </div>
              <div
                class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <ul
                  class="ant-menu ant-menu-sub ant-menu-inline"
                  data-menu-list="true"
                  id="rc-menu-uuid-sub3-popup"
                  role="menu"
                >
                  <li
                    aria-describedby="test-id"
                    class="ant-menu-item ant-menu-item-only-child"
                    data-menu-id="rc-menu-uuid-11"
                    role="menuitem"
                    style="padding-left: 72px;"
                    tabindex="-1"
                  >
                    <span
                      class="ant-menu-title-content"
                    >
                      Option 11
                    </span>
                  </li>
                  <div
                    class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                  >
                    <div
                      class="ant-tooltip-arrow"
                      style="position: absolute; top: 0px; left: 0px;"
                    />
                    <div
                      class="ant-tooltip-content"
                    >
                      <div
                        class="ant-tooltip-inner"
                        id="test-id"
                        role="tooltip"
                      />
                    </div>
                  </div>
                  <li
                    aria-describedby="test-id"
                    class="ant-menu-item ant-menu-item-only-child"
                    data-menu-id="rc-menu-uuid-12"
                    role="menuitem"
                    style="padding-left: 72px;"
                    tabindex="-1"
                  >
                    <span
                      class="ant-menu-title-content"
                    >
                      Option 12
                    </span>
                  </li>
                  <div
                    class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                  >
                    <div
                      class="ant-tooltip-arrow"
                      style="position: absolute; top: 0px; left: 0px;"
                    />
                    <div
                      class="ant-tooltip-content"
                    >
                      <div
                        class="ant-tooltip-inner"
                        id="test-id"
                        role="tooltip"
                      />
                    </div>
                  </div>
                </ul>
              </div>
            </li>
          </ul>
        </div>
      </li>
    </ul>
    <div
      aria-hidden="true"
      style="display: none;"
    />
  </div>
</div>
`;

exports[`renders components/menu/demo/component-token.tsx extend context correctly 2`] = `[]`;

exports[`renders components/menu/demo/custom-popup-render.tsx extend context correctly 1`] = `
Array [
  <ul
    class="ant-menu-overflow ant-menu ant-menu-root ant-menu-horizontal ant-menu-light css-var-test-id ant-menu-css-var"
    data-menu-list="true"
    role="menu"
    tabindex="0"
  >
    <li
      aria-describedby="test-id"
      class="ant-menu-overflow-item ant-menu-item ant-menu-item-only-child"
      data-menu-id="rc-menu-uuid-home"
      role="menuitem"
      style="opacity: 1; order: 0;"
      tabindex="-1"
    >
      <span
        class="ant-menu-title-content"
      >
        Home
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
    <li
      class="ant-menu-overflow-item ant-menu-submenu ant-menu-submenu-horizontal"
      role="none"
      style="opacity: 1; order: 1;"
    >
      <div
        aria-controls="rc-menu-uuid-features-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        role="menuitem"
        tabindex="-1"
      >
        <span
          class="ant-menu-title-content"
        >
          Features
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
    </li>
    <li
      class="ant-menu-overflow-item ant-menu-submenu ant-menu-submenu-horizontal"
      role="none"
      style="opacity: 1; order: 2;"
    >
      <div
        aria-controls="rc-menu-uuid-resources-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        role="menuitem"
        tabindex="-1"
      >
        <span
          class="ant-menu-title-content"
        >
          Resources
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
    </li>
    <li
      aria-hidden="true"
      class="ant-menu-overflow-item ant-menu-overflow-item-rest ant-menu-submenu ant-menu-submenu-horizontal"
      role="none"
      style="opacity: 0; height: 0px; overflow-y: hidden; order: 9007199254740991; pointer-events: none; position: absolute;"
    >
      <div
        aria-controls="rc-menu-uuid-rc-menu-more-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-rc-menu-more"
        role="menuitem"
        tabindex="-1"
      >
        <span
          aria-label="ellipsis"
          class="anticon anticon-ellipsis"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="ellipsis"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
            />
          </svg>
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var ant-menu-submenu-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="acss-kuniwg ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
        >
          <h3
            class="ant-typography acss-1s688k3 css-var-test-id"
          >
            <span
              aria-label="ellipsis"
              class="anticon anticon-ellipsis"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="ellipsis"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
                />
              </svg>
            </span>
          </h3>
          <div
            class="ant-row css-var-test-id"
            style="margin-inline: -8px;"
          />
        </div>
      </div>
    </li>
  </ul>,
  <div
    aria-hidden="true"
    style="display: none;"
  />,
]
`;

exports[`renders components/menu/demo/custom-popup-render.tsx extend context correctly 2`] = `[]`;

exports[`renders components/menu/demo/extra-style.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <ul
      class="ant-menu ant-menu-root ant-menu-inline ant-menu-light css-var-test-id ant-menu-css-var"
      data-menu-list="true"
      role="menu"
      style="width: 256px;"
      tabindex="0"
    >
      <li
        class="ant-menu-submenu ant-menu-submenu-inline ant-menu-submenu-open ant-menu-submenu-selected"
        role="none"
      >
        <div
          aria-controls="rc-menu-uuid-sub1-popup"
          aria-expanded="true"
          aria-haspopup="true"
          class="ant-menu-submenu-title"
          data-menu-id="rc-menu-uuid-sub1"
          role="menuitem"
          style="padding-left: 24px;"
          tabindex="-1"
        >
          <span
            aria-label="mail"
            class="anticon anticon-mail ant-menu-item-icon"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="mail"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"
              />
            </svg>
          </span>
          <span
            class="ant-menu-title-content"
          >
            Navigation One
          </span>
          <i
            class="ant-menu-submenu-arrow"
          />
        </div>
        <div
          class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <ul
            class="ant-menu ant-menu-sub ant-menu-inline"
            data-menu-list="true"
            id="rc-menu-uuid-sub1-popup"
            role="menu"
          >
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-selected ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-1"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                <div
                  class="ant-flex css-var-test-id ant-flex-justify-space-between"
                >
                  <span>
                    Option 1
                  </span>
                  <span
                    aria-label="down"
                    class="anticon anticon-down"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </div>
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
            <li
              aria-describedby="test-id"
              class="ant-menu-item"
              data-menu-id="rc-menu-uuid-2"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content ant-menu-title-content-with-extra"
              >
                Option 2
                <span
                  class="ant-menu-item-extra"
                >
                  ⌘P
                </span>
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
            <li
              aria-describedby="test-id"
              aria-disabled="true"
              class="ant-menu-item ant-menu-item-disabled ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-3"
              role="menuitem"
              style="padding-left: 48px;"
            >
              <span
                class="ant-menu-title-content"
              >
                <a
                  href="https://www.baidu.com"
                >
                  Link Option
                </a>
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
          </ul>
        </div>
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-sub1-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-selected ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-1"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              <div
                class="ant-flex css-var-test-id ant-flex-justify-space-between"
              >
                <span>
                  Option 1
                </span>
                <span
                  aria-label="down"
                  class="anticon anticon-down"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
              </div>
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item"
            data-menu-id="rc-menu-uuid-2"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content ant-menu-title-content-with-extra"
            >
              Option 2
              <span
                class="ant-menu-item-extra"
              >
                ⌘P
              </span>
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            aria-disabled="true"
            class="ant-menu-item ant-menu-item-disabled ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-3"
            role="menuitem"
            style="padding-left: 48px;"
          >
            <span
              class="ant-menu-title-content"
            >
              <a
                href="https://www.baidu.com"
              >
                Link Option
              </a>
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
        </ul>
      </li>
    </ul>
    <div
      aria-hidden="true"
      style="display: none;"
    />
  </div>
  <div
    class="ant-space-item"
  >
    <ul
      class="ant-menu ant-menu-root ant-menu-vertical ant-menu-dark css-var-test-id ant-menu-css-var"
      data-menu-list="true"
      role="menu"
      style="width: 256px;"
      tabindex="0"
    >
      <li
        aria-describedby="test-id"
        class="ant-menu-item"
        data-menu-id="rc-menu-uuid-1"
        role="menuitem"
        tabindex="-1"
      >
        <span
          class="ant-menu-title-content ant-menu-title-content-with-extra"
        >
          Users
          <span
            class="ant-menu-item-extra"
          >
            ⌘U
          </span>
        </span>
      </li>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; top: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          />
        </div>
      </div>
      <li
        aria-describedby="test-id"
        class="ant-menu-item"
        data-menu-id="rc-menu-uuid-2"
        role="menuitem"
        tabindex="-1"
      >
        <span
          class="ant-menu-title-content ant-menu-title-content-with-extra"
        >
          Profile
          <span
            class="ant-menu-item-extra"
          >
            ⌘P
          </span>
        </span>
      </li>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; top: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          />
        </div>
      </div>
    </ul>
    <div
      aria-hidden="true"
      style="display: none;"
    />
  </div>
</div>
`;

exports[`renders components/menu/demo/extra-style.tsx extend context correctly 2`] = `[]`;

exports[`renders components/menu/demo/horizontal.tsx extend context correctly 1`] = `
Array [
  <ul
    class="ant-menu-overflow ant-menu ant-menu-root ant-menu-horizontal ant-menu-light css-var-test-id ant-menu-css-var"
    data-menu-list="true"
    role="menu"
    tabindex="0"
  >
    <li
      aria-describedby="test-id"
      class="ant-menu-overflow-item ant-menu-item ant-menu-item-selected"
      data-menu-id="rc-menu-uuid-mail"
      role="menuitem"
      style="opacity: 1; order: 0;"
      tabindex="-1"
    >
      <span
        aria-label="mail"
        class="anticon anticon-mail ant-menu-item-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="mail"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"
          />
        </svg>
      </span>
      <span
        class="ant-menu-title-content"
      >
        Navigation One
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
    <li
      aria-describedby="test-id"
      aria-disabled="true"
      class="ant-menu-overflow-item ant-menu-item ant-menu-item-disabled"
      role="menuitem"
      style="opacity: 1; order: 1;"
    >
      <span
        aria-label="appstore"
        class="anticon anticon-appstore ant-menu-item-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="appstore"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"
          />
        </svg>
      </span>
      <span
        class="ant-menu-title-content"
      >
        Navigation Two
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
    <li
      class="ant-menu-overflow-item ant-menu-submenu ant-menu-submenu-horizontal"
      role="none"
      style="opacity: 1; order: 2;"
    >
      <div
        aria-controls="rc-menu-uuid-SubMenu-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        role="menuitem"
        tabindex="-1"
      >
        <span
          aria-label="setting"
          class="anticon anticon-setting ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="setting"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Three - Submenu
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
    </li>
    <li
      aria-describedby="test-id"
      class="ant-menu-overflow-item ant-menu-item ant-menu-item-only-child"
      role="menuitem"
      style="opacity: 1; order: 3;"
      tabindex="-1"
    >
      <span
        class="ant-menu-title-content"
      >
        <a
          href="https://ant.design"
          rel="noopener noreferrer"
          target="_blank"
        >
          Navigation Four - Link
        </a>
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
    <li
      aria-hidden="true"
      class="ant-menu-overflow-item ant-menu-overflow-item-rest ant-menu-submenu ant-menu-submenu-horizontal"
      role="none"
      style="opacity: 0; height: 0px; overflow-y: hidden; order: 9007199254740991; pointer-events: none; position: absolute;"
    >
      <div
        aria-controls="rc-menu-uuid-rc-menu-more-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-rc-menu-more"
        role="menuitem"
        tabindex="-1"
      >
        <span
          aria-label="ellipsis"
          class="anticon anticon-ellipsis"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="ellipsis"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
            />
          </svg>
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var ant-menu-submenu-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-vertical"
          data-menu-list="true"
          id="rc-menu-uuid-rc-menu-more-popup"
          role="menu"
        />
      </div>
    </li>
  </ul>,
  <div
    aria-hidden="true"
    style="display: none;"
  />,
]
`;

exports[`renders components/menu/demo/horizontal.tsx extend context correctly 2`] = `[]`;

exports[`renders components/menu/demo/horizontal-dark.tsx extend context correctly 1`] = `
Array [
  <ul
    class="ant-menu-overflow ant-menu ant-menu-root ant-menu-horizontal ant-menu-dark css-var-test-id ant-menu-css-var"
    data-menu-list="true"
    role="menu"
    tabindex="0"
  >
    <li
      aria-describedby="test-id"
      class="ant-menu-overflow-item ant-menu-item ant-menu-item-selected"
      data-menu-id="rc-menu-uuid-mail"
      role="menuitem"
      style="opacity: 1; order: 0;"
      tabindex="-1"
    >
      <span
        aria-label="mail"
        class="anticon anticon-mail ant-menu-item-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="mail"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"
          />
        </svg>
      </span>
      <span
        class="ant-menu-title-content"
      >
        Navigation One
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
    <li
      aria-describedby="test-id"
      aria-disabled="true"
      class="ant-menu-overflow-item ant-menu-item ant-menu-item-disabled"
      role="menuitem"
      style="opacity: 1; order: 1;"
    >
      <span
        aria-label="appstore"
        class="anticon anticon-appstore ant-menu-item-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="appstore"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"
          />
        </svg>
      </span>
      <span
        class="ant-menu-title-content"
      >
        Navigation Two
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
    <li
      class="ant-menu-overflow-item ant-menu-submenu ant-menu-submenu-horizontal"
      role="none"
      style="opacity: 1; order: 2;"
    >
      <div
        aria-controls="rc-menu-uuid-SubMenu-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        role="menuitem"
        tabindex="-1"
      >
        <span
          aria-label="setting"
          class="anticon anticon-setting ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="setting"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Three - Submenu
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
    </li>
    <li
      aria-describedby="test-id"
      class="ant-menu-overflow-item ant-menu-item ant-menu-item-only-child"
      role="menuitem"
      style="opacity: 1; order: 3;"
      tabindex="-1"
    >
      <span
        class="ant-menu-title-content"
      >
        <a
          href="https://ant.design"
          rel="noopener noreferrer"
          target="_blank"
        >
          Navigation Four - Link
        </a>
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
    <li
      aria-hidden="true"
      class="ant-menu-overflow-item ant-menu-overflow-item-rest ant-menu-submenu ant-menu-submenu-horizontal"
      role="none"
      style="opacity: 0; height: 0px; overflow-y: hidden; order: 9007199254740991; pointer-events: none; position: absolute;"
    >
      <div
        aria-controls="rc-menu-uuid-rc-menu-more-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-rc-menu-more"
        role="menuitem"
        tabindex="-1"
      >
        <span
          aria-label="ellipsis"
          class="anticon anticon-ellipsis"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="ellipsis"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
            />
          </svg>
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var ant-menu-submenu-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-vertical"
          data-menu-list="true"
          id="rc-menu-uuid-rc-menu-more-popup"
          role="menu"
        />
      </div>
    </li>
  </ul>,
  <div
    aria-hidden="true"
    style="display: none;"
  />,
]
`;

exports[`renders components/menu/demo/horizontal-dark.tsx extend context correctly 2`] = `[]`;

exports[`renders components/menu/demo/inline.tsx extend context correctly 1`] = `
Array [
  <ul
    class="ant-menu ant-menu-root ant-menu-inline ant-menu-light css-var-test-id ant-menu-css-var"
    data-menu-list="true"
    role="menu"
    style="width: 256px;"
    tabindex="0"
  >
    <li
      class="ant-menu-submenu ant-menu-submenu-inline ant-menu-submenu-open ant-menu-submenu-selected"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-sub1-popup"
        aria-expanded="true"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub1"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="mail"
          class="anticon anticon-mail ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="mail"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation One
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-sub1-popup"
          role="menu"
        >
          <li
            class="ant-menu-item-group"
            role="presentation"
          >
            <div
              class="ant-menu-item-group-title"
              role="presentation"
              title="Item 1"
            >
              Item 1
            </div>
            <ul
              class="ant-menu-item-group-list"
              role="group"
            >
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-selected ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-1"
                role="menuitem"
                style="padding-left: 48px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 1
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-2"
                role="menuitem"
                style="padding-left: 48px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 2
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
            </ul>
          </li>
          <li
            class="ant-menu-item-group"
            role="presentation"
          >
            <div
              class="ant-menu-item-group-title"
              role="presentation"
              title="Item 2"
            >
              Item 2
            </div>
            <ul
              class="ant-menu-item-group-list"
              role="group"
            >
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-3"
                role="menuitem"
                style="padding-left: 48px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 3
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-4"
                role="menuitem"
                style="padding-left: 48px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 4
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
            </ul>
          </li>
        </ul>
      </div>
      <ul
        class="ant-menu ant-menu-sub ant-menu-inline"
        data-menu-list="true"
        id="rc-menu-uuid-sub1-popup"
        role="menu"
      >
        <li
          class="ant-menu-item-group"
          role="presentation"
        >
          <div
            class="ant-menu-item-group-title"
            role="presentation"
            title="Item 1"
          >
            Item 1
          </div>
          <ul
            class="ant-menu-item-group-list"
            role="group"
          >
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-selected ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-1"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 1
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-2"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 2
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
          </ul>
        </li>
        <li
          class="ant-menu-item-group"
          role="presentation"
        >
          <div
            class="ant-menu-item-group-title"
            role="presentation"
            title="Item 2"
          >
            Item 2
          </div>
          <ul
            class="ant-menu-item-group-list"
            role="group"
          >
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-3"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 3
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-4"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 4
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
          </ul>
        </li>
      </ul>
    </li>
    <li
      class="ant-menu-submenu ant-menu-submenu-inline"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-sub2-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub2"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="appstore"
          class="anticon anticon-appstore ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="appstore"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Two
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-sub2-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-5"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 5
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-6"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 6
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            class="ant-menu-submenu ant-menu-submenu-inline"
            role="none"
          >
            <div
              aria-controls="rc-menu-uuid-sub3-popup"
              aria-expanded="false"
              aria-haspopup="true"
              class="ant-menu-submenu-title"
              data-menu-id="rc-menu-uuid-sub3"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Submenu
              </span>
              <i
                class="ant-menu-submenu-arrow"
              />
            </div>
            <div
              class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <ul
                class="ant-menu ant-menu-sub ant-menu-inline"
                data-menu-list="true"
                id="rc-menu-uuid-sub3-popup"
                role="menu"
              >
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-7"
                  role="menuitem"
                  style="padding-left: 72px;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 7
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-8"
                  role="menuitem"
                  style="padding-left: 72px;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 8
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
              </ul>
            </div>
          </li>
        </ul>
      </div>
    </li>
    <li
      class="ant-menu-item-divider"
      role="separator"
    />
    <li
      class="ant-menu-submenu ant-menu-submenu-inline"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-sub4-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub4"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="setting"
          class="anticon anticon-setting ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="setting"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Three
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-sub4-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-9"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 9
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-10"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 10
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-11"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 11
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-12"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 12
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
        </ul>
      </div>
    </li>
    <li
      class="ant-menu-item-group"
      role="presentation"
    >
      <div
        class="ant-menu-item-group-title"
        role="presentation"
        title="Group"
      >
        Group
      </div>
      <ul
        class="ant-menu-item-group-list"
        role="group"
      >
        <li
          aria-describedby="test-id"
          class="ant-menu-item ant-menu-item-only-child"
          data-menu-id="rc-menu-uuid-13"
          role="menuitem"
          style="padding-left: 24px;"
          tabindex="-1"
        >
          <span
            class="ant-menu-title-content"
          >
            Option 13
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
        <li
          aria-describedby="test-id"
          class="ant-menu-item ant-menu-item-only-child"
          data-menu-id="rc-menu-uuid-14"
          role="menuitem"
          style="padding-left: 24px;"
          tabindex="-1"
        >
          <span
            class="ant-menu-title-content"
          >
            Option 14
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
      </ul>
    </li>
  </ul>,
  <div
    aria-hidden="true"
    style="display: none;"
  />,
]
`;

exports[`renders components/menu/demo/inline.tsx extend context correctly 2`] = `[]`;

exports[`renders components/menu/demo/inline-collapsed.tsx extend context correctly 1`] = `
<div
  style="width: 256px;"
>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    style="margin-bottom: 16px;"
    type="button"
  >
    <span
      aria-label="menu-fold"
      class="anticon anticon-menu-fold"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="menu-fold"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 000 13.8z"
        />
      </svg>
    </span>
  </button>
  <ul
    class="ant-menu ant-menu-root ant-menu-inline ant-menu-dark css-var-test-id ant-menu-css-var"
    data-menu-list="true"
    role="menu"
    tabindex="0"
  >
    <li
      aria-describedby="test-id"
      class="ant-menu-item ant-menu-item-selected"
      data-menu-id="rc-menu-uuid-1"
      role="menuitem"
      style="padding-left: 24px;"
      tabindex="-1"
    >
      <span
        aria-label="pie-chart"
        class="anticon anticon-pie-chart ant-menu-item-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="pie-chart"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M864 518H506V160c0-4.4-3.6-8-8-8h-26a398.46 398.46 0 00-282.8 117.1 398.19 398.19 0 00-85.7 127.1A397.61 397.61 0 0072 552a398.46 398.46 0 00117.1 282.8c36.7 36.7 79.5 65.6 127.1 85.7A397.61 397.61 0 00472 952a398.46 398.46 0 00282.8-117.1c36.7-36.7 65.6-79.5 85.7-127.1A397.61 397.61 0 00872 552v-26c0-4.4-3.6-8-8-8zM705.7 787.8A331.59 331.59 0 01470.4 884c-88.1-.4-170.9-34.9-233.2-97.2C174.5 724.1 140 640.7 140 552c0-88.7 34.5-172.1 97.2-234.8 54.6-54.6 124.9-87.9 200.8-95.5V586h364.3c-7.7 76.3-41.3 147-96.6 201.8zM952 462.4l-2.6-28.2c-8.5-92.1-49.4-179-115.2-244.6A399.4 399.4 0 00589 74.6L560.7 72c-4.7-.4-8.7 3.2-8.7 7.9V464c0 4.4 3.6 8 8 8l384-1c4.7 0 8.4-4 8-8.6zm-332.2-58.2V147.6a332.24 332.24 0 01166.4 89.8c45.7 45.6 77 103.6 90 166.1l-256.4.7z"
          />
        </svg>
      </span>
      <span
        class="ant-menu-title-content"
      >
        Option 1
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
    <li
      aria-describedby="test-id"
      class="ant-menu-item"
      data-menu-id="rc-menu-uuid-2"
      role="menuitem"
      style="padding-left: 24px;"
      tabindex="-1"
    >
      <span
        aria-label="desktop"
        class="anticon anticon-desktop ant-menu-item-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="desktop"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M928 140H96c-17.7 0-32 14.3-32 32v496c0 17.7 14.3 32 32 32h380v112H304c-8.8 0-16 7.2-16 16v48c0 4.4 3.6 8 8 8h432c4.4 0 8-3.6 8-8v-48c0-8.8-7.2-16-16-16H548V700h380c17.7 0 32-14.3 32-32V172c0-17.7-14.3-32-32-32zm-40 488H136V212h752v416z"
          />
        </svg>
      </span>
      <span
        class="ant-menu-title-content"
      >
        Option 2
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
    <li
      aria-describedby="test-id"
      class="ant-menu-item"
      data-menu-id="rc-menu-uuid-3"
      role="menuitem"
      style="padding-left: 24px;"
      tabindex="-1"
    >
      <span
        aria-label="container"
        class="anticon anticon-container ant-menu-item-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="container"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-40 824H232V687h97.9c11.6 32.8 32 62.3 59.1 84.7 34.5 28.5 78.2 44.3 123 44.3s88.5-15.7 123-44.3c27.1-22.4 47.5-51.9 59.1-84.7H792v-63H643.6l-5.2 24.7C626.4 708.5 573.2 752 512 752s-114.4-43.5-126.5-103.3l-5.2-24.7H232V136h560v752zM320 341h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zm0 160h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"
          />
        </svg>
      </span>
      <span
        class="ant-menu-title-content"
      >
        Option 3
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
    <li
      class="ant-menu-submenu ant-menu-submenu-inline ant-menu-submenu-open"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-sub1-popup"
        aria-expanded="true"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub1"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="mail"
          class="anticon anticon-mail ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="mail"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation One
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-sub1-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-5"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 5
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-6"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 6
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-7"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 7
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-8"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 8
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
        </ul>
      </div>
      <ul
        class="ant-menu ant-menu-sub ant-menu-inline"
        data-menu-list="true"
        id="rc-menu-uuid-sub1-popup"
        role="menu"
      >
        <li
          aria-describedby="test-id"
          class="ant-menu-item ant-menu-item-only-child"
          data-menu-id="rc-menu-uuid-5"
          role="menuitem"
          style="padding-left: 48px;"
          tabindex="-1"
        >
          <span
            class="ant-menu-title-content"
          >
            Option 5
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
        <li
          aria-describedby="test-id"
          class="ant-menu-item ant-menu-item-only-child"
          data-menu-id="rc-menu-uuid-6"
          role="menuitem"
          style="padding-left: 48px;"
          tabindex="-1"
        >
          <span
            class="ant-menu-title-content"
          >
            Option 6
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
        <li
          aria-describedby="test-id"
          class="ant-menu-item ant-menu-item-only-child"
          data-menu-id="rc-menu-uuid-7"
          role="menuitem"
          style="padding-left: 48px;"
          tabindex="-1"
        >
          <span
            class="ant-menu-title-content"
          >
            Option 7
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
        <li
          aria-describedby="test-id"
          class="ant-menu-item ant-menu-item-only-child"
          data-menu-id="rc-menu-uuid-8"
          role="menuitem"
          style="padding-left: 48px;"
          tabindex="-1"
        >
          <span
            class="ant-menu-title-content"
          >
            Option 8
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
      </ul>
    </li>
    <li
      class="ant-menu-submenu ant-menu-submenu-inline"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-sub2-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub2"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="appstore"
          class="anticon anticon-appstore ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="appstore"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Two
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-sub2-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-9"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 9
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-10"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 10
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            class="ant-menu-submenu ant-menu-submenu-inline"
            role="none"
          >
            <div
              aria-controls="rc-menu-uuid-sub3-popup"
              aria-expanded="false"
              aria-haspopup="true"
              class="ant-menu-submenu-title"
              data-menu-id="rc-menu-uuid-sub3"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Submenu
              </span>
              <i
                class="ant-menu-submenu-arrow"
              />
            </div>
            <div
              class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <ul
                class="ant-menu ant-menu-sub ant-menu-inline"
                data-menu-list="true"
                id="rc-menu-uuid-sub3-popup"
                role="menu"
              >
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-11"
                  role="menuitem"
                  style="padding-left: 72px;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 11
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-12"
                  role="menuitem"
                  style="padding-left: 72px;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 12
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
              </ul>
            </div>
          </li>
        </ul>
      </div>
    </li>
  </ul>
  <div
    aria-hidden="true"
    style="display: none;"
  />
</div>
`;

exports[`renders components/menu/demo/inline-collapsed.tsx extend context correctly 2`] = `[]`;

exports[`renders components/menu/demo/menu-v4.tsx extend context correctly 1`] = `
Array [
  <button
    aria-checked="false"
    class="ant-switch css-var-test-id"
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      />
      <span
        class="ant-switch-inner-unchecked"
      />
    </span>
  </button>,
   Change Mode,
  <br />,
  <br />,
  <ul
    class="ant-menu ant-menu-root ant-menu-inline ant-menu-light css-var-test-id ant-menu-css-var"
    data-menu-list="true"
    role="menu"
    style="width: 256px;"
    tabindex="0"
  >
    <li
      aria-describedby="test-id"
      class="ant-menu-item ant-menu-item-selected"
      data-menu-id="rc-menu-uuid-1"
      role="menuitem"
      style="padding-left: 24px;"
      tabindex="-1"
    >
      <span
        aria-label="mail"
        class="anticon anticon-mail ant-menu-item-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="mail"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"
          />
        </svg>
      </span>
      <span
        class="ant-menu-title-content"
      >
        Navigation One
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
    <li
      aria-describedby="test-id"
      class="ant-menu-item"
      data-menu-id="rc-menu-uuid-2"
      role="menuitem"
      style="padding-left: 24px;"
      tabindex="-1"
    >
      <span
        aria-label="calendar"
        class="anticon anticon-calendar ant-menu-item-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="calendar"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
          />
        </svg>
      </span>
      <span
        class="ant-menu-title-content"
      >
        Navigation Two
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
    <li
      class="ant-menu-submenu ant-menu-submenu-inline ant-menu-submenu-open"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-sub1-popup"
        aria-expanded="true"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub1"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="appstore"
          class="anticon anticon-appstore ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="appstore"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Two
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-sub1-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-3"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              <span
                class="ant-typography ant-typography-ellipsis ant-typography-ellipsis-single-line css-var-test-id"
              >
                Ant Design, a design language for background applications, is refined by Ant UED Team
              </span>
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-4"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 4
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            class="ant-menu-submenu ant-menu-submenu-inline"
            role="none"
          >
            <div
              aria-controls="rc-menu-uuid-sub1-2-popup"
              aria-expanded="false"
              aria-haspopup="true"
              class="ant-menu-submenu-title"
              data-menu-id="rc-menu-uuid-sub1-2"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Submenu
              </span>
              <i
                class="ant-menu-submenu-arrow"
              />
            </div>
            <div
              class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <ul
                class="ant-menu ant-menu-sub ant-menu-inline"
                data-menu-list="true"
                id="rc-menu-uuid-sub1-2-popup"
                role="menu"
              >
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-5"
                  role="menuitem"
                  style="padding-left: 72px;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 5
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-6"
                  role="menuitem"
                  style="padding-left: 72px;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 6
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
              </ul>
            </div>
          </li>
        </ul>
      </div>
      <ul
        class="ant-menu ant-menu-sub ant-menu-inline"
        data-menu-list="true"
        id="rc-menu-uuid-sub1-popup"
        role="menu"
      >
        <li
          aria-describedby="test-id"
          class="ant-menu-item ant-menu-item-only-child"
          data-menu-id="rc-menu-uuid-3"
          role="menuitem"
          style="padding-left: 48px;"
          tabindex="-1"
        >
          <span
            class="ant-menu-title-content"
          >
            <span
              class="ant-typography ant-typography-ellipsis ant-typography-ellipsis-single-line css-var-test-id"
            >
              Ant Design, a design language for background applications, is refined by Ant UED Team
            </span>
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
        <li
          aria-describedby="test-id"
          class="ant-menu-item ant-menu-item-only-child"
          data-menu-id="rc-menu-uuid-4"
          role="menuitem"
          style="padding-left: 48px;"
          tabindex="-1"
        >
          <span
            class="ant-menu-title-content"
          >
            Option 4
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
        <li
          class="ant-menu-submenu ant-menu-submenu-inline"
          role="none"
        >
          <div
            aria-controls="rc-menu-uuid-sub1-2-popup"
            aria-expanded="false"
            aria-haspopup="true"
            class="ant-menu-submenu-title"
            data-menu-id="rc-menu-uuid-sub1-2"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Submenu
            </span>
            <i
              class="ant-menu-submenu-arrow"
            />
          </div>
          <div
            class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <ul
              class="ant-menu ant-menu-sub ant-menu-inline"
              data-menu-list="true"
              id="rc-menu-uuid-sub1-2-popup"
              role="menu"
            >
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-5"
                role="menuitem"
                style="padding-left: 72px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 5
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-6"
                role="menuitem"
                style="padding-left: 72px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 6
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
            </ul>
          </div>
        </li>
      </ul>
    </li>
    <li
      class="ant-menu-submenu ant-menu-submenu-inline"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-sub2-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub2"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="setting"
          class="anticon anticon-setting ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="setting"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Three
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-sub2-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-7"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 7
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-8"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 8
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-9"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 9
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-10"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 10
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
        </ul>
      </div>
    </li>
    <li
      aria-describedby="test-id"
      class="ant-menu-item"
      data-menu-id="rc-menu-uuid-link"
      role="menuitem"
      style="padding-left: 24px;"
      tabindex="-1"
    >
      <span
        aria-label="link"
        class="anticon anticon-link ant-menu-item-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="link"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M574 665.4a8.03 8.03 0 00-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 00-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 000 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 000 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 00-11.3 0L372.3 598.7a8.03 8.03 0 000 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z"
          />
        </svg>
      </span>
      <span
        class="ant-menu-title-content"
      >
        <a
          href="https://ant.design"
          rel="noopener noreferrer"
          target="_blank"
        >
          Ant Design
        </a>
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
  </ul>,
  <div
    aria-hidden="true"
    style="display: none;"
  />,
]
`;

exports[`renders components/menu/demo/menu-v4.tsx extend context correctly 2`] = `[]`;

exports[`renders components/menu/demo/sider-current.tsx extend context correctly 1`] = `
Array [
  <ul
    class="ant-menu ant-menu-root ant-menu-inline ant-menu-light css-var-test-id ant-menu-css-var"
    data-menu-list="true"
    role="menu"
    style="width: 256px;"
    tabindex="0"
  >
    <li
      class="ant-menu-submenu ant-menu-submenu-inline"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-1-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-1"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="mail"
          class="anticon anticon-mail ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="mail"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation One
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-1-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-11"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 1
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-12"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 2
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-13"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 3
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-14"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 4
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
        </ul>
      </div>
    </li>
    <li
      class="ant-menu-submenu ant-menu-submenu-inline ant-menu-submenu-open ant-menu-submenu-selected"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-2-popup"
        aria-expanded="true"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-2"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="appstore"
          class="anticon anticon-appstore ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="appstore"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Two
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-2-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-21"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 1
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-22"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 2
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            class="ant-menu-submenu ant-menu-submenu-inline ant-menu-submenu-open ant-menu-submenu-selected"
            role="none"
          >
            <div
              aria-controls="rc-menu-uuid-23-popup"
              aria-expanded="true"
              aria-haspopup="true"
              class="ant-menu-submenu-title"
              data-menu-id="rc-menu-uuid-23"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Submenu
              </span>
              <i
                class="ant-menu-submenu-arrow"
              />
            </div>
            <div
              class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <ul
                class="ant-menu ant-menu-sub ant-menu-inline"
                data-menu-list="true"
                id="rc-menu-uuid-23-popup"
                role="menu"
              >
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-selected ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-231"
                  role="menuitem"
                  style="padding-left: 72px;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 1
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-232"
                  role="menuitem"
                  style="padding-left: 72px;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 2
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-233"
                  role="menuitem"
                  style="padding-left: 72px;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 3
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
              </ul>
            </div>
            <ul
              class="ant-menu ant-menu-sub ant-menu-inline"
              data-menu-list="true"
              id="rc-menu-uuid-23-popup"
              role="menu"
            >
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-selected ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-231"
                role="menuitem"
                style="padding-left: 72px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 1
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-232"
                role="menuitem"
                style="padding-left: 72px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 2
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-233"
                role="menuitem"
                style="padding-left: 72px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 3
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
            </ul>
          </li>
          <li
            class="ant-menu-submenu ant-menu-submenu-inline"
            role="none"
          >
            <div
              aria-controls="rc-menu-uuid-24-popup"
              aria-expanded="false"
              aria-haspopup="true"
              class="ant-menu-submenu-title"
              data-menu-id="rc-menu-uuid-24"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Submenu 2
              </span>
              <i
                class="ant-menu-submenu-arrow"
              />
            </div>
            <div
              class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <ul
                class="ant-menu ant-menu-sub ant-menu-inline"
                data-menu-list="true"
                id="rc-menu-uuid-24-popup"
                role="menu"
              >
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-241"
                  role="menuitem"
                  style="padding-left: 72px;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 1
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-242"
                  role="menuitem"
                  style="padding-left: 72px;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 2
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-243"
                  role="menuitem"
                  style="padding-left: 72px;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 3
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
              </ul>
            </div>
          </li>
        </ul>
      </div>
      <ul
        class="ant-menu ant-menu-sub ant-menu-inline"
        data-menu-list="true"
        id="rc-menu-uuid-2-popup"
        role="menu"
      >
        <li
          aria-describedby="test-id"
          class="ant-menu-item ant-menu-item-only-child"
          data-menu-id="rc-menu-uuid-21"
          role="menuitem"
          style="padding-left: 48px;"
          tabindex="-1"
        >
          <span
            class="ant-menu-title-content"
          >
            Option 1
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
        <li
          aria-describedby="test-id"
          class="ant-menu-item ant-menu-item-only-child"
          data-menu-id="rc-menu-uuid-22"
          role="menuitem"
          style="padding-left: 48px;"
          tabindex="-1"
        >
          <span
            class="ant-menu-title-content"
          >
            Option 2
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
        <li
          class="ant-menu-submenu ant-menu-submenu-inline ant-menu-submenu-open ant-menu-submenu-selected"
          role="none"
        >
          <div
            aria-controls="rc-menu-uuid-23-popup"
            aria-expanded="true"
            aria-haspopup="true"
            class="ant-menu-submenu-title"
            data-menu-id="rc-menu-uuid-23"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Submenu
            </span>
            <i
              class="ant-menu-submenu-arrow"
            />
          </div>
          <div
            class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <ul
              class="ant-menu ant-menu-sub ant-menu-inline"
              data-menu-list="true"
              id="rc-menu-uuid-23-popup"
              role="menu"
            >
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-selected ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-231"
                role="menuitem"
                style="padding-left: 72px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 1
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-232"
                role="menuitem"
                style="padding-left: 72px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 2
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-233"
                role="menuitem"
                style="padding-left: 72px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 3
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
            </ul>
          </div>
          <ul
            class="ant-menu ant-menu-sub ant-menu-inline"
            data-menu-list="true"
            id="rc-menu-uuid-23-popup"
            role="menu"
          >
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-selected ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-231"
              role="menuitem"
              style="padding-left: 72px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 1
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-232"
              role="menuitem"
              style="padding-left: 72px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 2
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
            <li
              aria-describedby="test-id"
              class="ant-menu-item ant-menu-item-only-child"
              data-menu-id="rc-menu-uuid-233"
              role="menuitem"
              style="padding-left: 72px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Option 3
              </span>
            </li>
            <div
              class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div
                class="ant-tooltip-arrow"
                style="position: absolute; top: 0px; left: 0px;"
              />
              <div
                class="ant-tooltip-content"
              >
                <div
                  class="ant-tooltip-inner"
                  id="test-id"
                  role="tooltip"
                />
              </div>
            </div>
          </ul>
        </li>
        <li
          class="ant-menu-submenu ant-menu-submenu-inline"
          role="none"
        >
          <div
            aria-controls="rc-menu-uuid-24-popup"
            aria-expanded="false"
            aria-haspopup="true"
            class="ant-menu-submenu-title"
            data-menu-id="rc-menu-uuid-24"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Submenu 2
            </span>
            <i
              class="ant-menu-submenu-arrow"
            />
          </div>
          <div
            class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <ul
              class="ant-menu ant-menu-sub ant-menu-inline"
              data-menu-list="true"
              id="rc-menu-uuid-24-popup"
              role="menu"
            >
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-241"
                role="menuitem"
                style="padding-left: 72px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 1
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-242"
                role="menuitem"
                style="padding-left: 72px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 2
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-243"
                role="menuitem"
                style="padding-left: 72px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 3
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
            </ul>
          </div>
        </li>
      </ul>
    </li>
    <li
      class="ant-menu-submenu ant-menu-submenu-inline"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-3-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-3"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="setting"
          class="anticon anticon-setting ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="setting"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Three
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-3-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-31"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 1
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-32"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 2
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-33"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 3
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-34"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 4
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
        </ul>
      </div>
    </li>
  </ul>,
  <div
    aria-hidden="true"
    style="display: none;"
  />,
]
`;

exports[`renders components/menu/demo/sider-current.tsx extend context correctly 2`] = `[]`;

exports[`renders components/menu/demo/style-class.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <ul
    class="ant-menu ant-menu-root ant-menu-inline ant-menu-light css-var-test-id ant-menu-css-var demo-menu-root"
    data-menu-list="true"
    role="menu"
    tabindex="0"
  >
    <li
      class="ant-menu-submenu ant-menu-submenu-inline"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-SubMenu-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-SubMenu"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          class="ant-menu-title-content"
        >
          Navigation One
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var demo-menu-root"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-SubMenu-popup"
          role="menu"
        >
          <li
            class="ant-menu-item-group"
            role="presentation"
          >
            <div
              class="ant-menu-item-group-title demo-submenu-title"
              role="presentation"
              title="Item 1"
            >
              Item 1
            </div>
            <ul
              class="ant-menu-item-group-list demo-submenu-list"
              role="group"
            >
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-1"
                role="menuitem"
                style="padding-left: 48px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 1
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-2"
                role="menuitem"
                style="padding-left: 48px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 2
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
            </ul>
          </li>
        </ul>
      </div>
    </li>
    <li
      aria-describedby="test-id"
      class="ant-menu-item demo-menu-item ant-menu-item-only-child"
      data-menu-id="rc-menu-uuid-mail"
      role="menuitem"
      style="padding-left: 24px;"
      tabindex="-1"
    >
      <span
        class="ant-menu-title-content demo-menu-item-content"
      >
        Navigation Two
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
  </ul>
  <div
    aria-hidden="true"
    style="display: none;"
  />
  <ul
    class="ant-menu ant-menu-root ant-menu-inline ant-menu-light css-var-test-id ant-menu-css-var demo-menu-root--grouped"
    data-menu-list="true"
    role="menu"
    tabindex="0"
  >
    <li
      class="ant-menu-submenu ant-menu-submenu-inline"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-SubMenu-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-SubMenu"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          class="ant-menu-title-content"
        >
          Navigation One
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var demo-menu-root--grouped"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-SubMenu-popup"
          role="menu"
        >
          <li
            class="ant-menu-item-group"
            role="presentation"
          >
            <div
              class="ant-menu-item-group-title"
              role="presentation"
              title="Item 1"
            >
              Item 1
            </div>
            <ul
              class="ant-menu-item-group-list"
              role="group"
            >
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-1"
                role="menuitem"
                style="padding-left: 48px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 1
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-2"
                role="menuitem"
                style="padding-left: 48px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 2
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
            </ul>
          </li>
        </ul>
      </div>
    </li>
    <li
      aria-describedby="test-id"
      class="ant-menu-item ant-menu-item-only-child"
      data-menu-id="rc-menu-uuid-mail"
      role="menuitem"
      style="padding-left: 24px;"
      tabindex="-1"
    >
      <span
        class="ant-menu-title-content"
      >
        Navigation Two
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
  </ul>
  <div
    aria-hidden="true"
    style="display: none;"
  />
  <ul
    class="ant-menu ant-menu-root ant-menu-inline ant-menu-light css-var-test-id ant-menu-css-var"
    data-menu-list="true"
    role="menu"
    style="border: 1px solid rgb(240, 240, 240); padding: 8px; border-radius: 4px;"
    tabindex="0"
  >
    <li
      class="ant-menu-submenu ant-menu-submenu-inline"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-SubMenu-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-SubMenu"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          class="ant-menu-title-content"
        >
          Navigation One
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-SubMenu-popup"
          role="menu"
        >
          <li
            class="ant-menu-item-group"
            role="presentation"
          >
            <div
              class="ant-menu-item-group-title"
              role="presentation"
              title="Item 1"
            >
              Item 1
            </div>
            <ul
              class="ant-menu-item-group-list"
              role="group"
              style="color: rgb(250, 84, 28);"
            >
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-1"
                role="menuitem"
                style="padding-left: 48px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 1
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-2"
                role="menuitem"
                style="padding-left: 48px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 2
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
            </ul>
          </li>
        </ul>
      </div>
    </li>
    <li
      aria-describedby="test-id"
      class="ant-menu-item ant-menu-item-only-child"
      data-menu-id="rc-menu-uuid-mail"
      role="menuitem"
      style="padding-left: 24px; color: rgb(22, 119, 255);"
      tabindex="-1"
    >
      <span
        class="ant-menu-title-content"
      >
        Navigation Two
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
  </ul>
  <div
    aria-hidden="true"
    style="display: none;"
  />
  <ul
    class="ant-menu ant-menu-root ant-menu-inline ant-menu-light css-var-test-id ant-menu-css-var"
    data-menu-list="true"
    role="menu"
    style="background-color: rgb(240, 249, 255);"
    tabindex="0"
  >
    <li
      class="ant-menu-submenu ant-menu-submenu-inline"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-SubMenu-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-SubMenu"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          class="ant-menu-title-content"
        >
          Navigation One
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-SubMenu-popup"
          role="menu"
        >
          <li
            class="ant-menu-item-group"
            role="presentation"
          >
            <div
              class="ant-menu-item-group-title"
              role="presentation"
              title="Item 1"
            >
              Item 1
            </div>
            <ul
              class="ant-menu-item-group-list"
              role="group"
            >
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-1"
                role="menuitem"
                style="padding-left: 48px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 1
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-2"
                role="menuitem"
                style="padding-left: 48px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 2
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
            </ul>
          </li>
        </ul>
      </div>
    </li>
    <li
      aria-describedby="test-id"
      class="ant-menu-item ant-menu-item-only-child"
      data-menu-id="rc-menu-uuid-mail"
      role="menuitem"
      style="padding-left: 24px;"
      tabindex="-1"
    >
      <span
        class="ant-menu-title-content"
      >
        Navigation Two
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
  </ul>
  <div
    aria-hidden="true"
    style="display: none;"
  />
</div>
`;

exports[`renders components/menu/demo/style-class.tsx extend context correctly 2`] = `[]`;

exports[`renders components/menu/demo/style-debug.tsx extend context correctly 1`] = `
Array [
  <button
    aria-checked="true"
    class="ant-switch css-var-test-id ant-switch-checked"
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      >
        Dark
      </span>
      <span
        class="ant-switch-inner-unchecked"
      >
        Light
      </span>
    </span>
  </button>,
  <br />,
  <br />,
  <ul
    class="ant-menu ant-menu-root ant-menu-vertical ant-menu-dark ant-menu-inline-collapsed css-var-test-id ant-menu-css-var"
    data-menu-list="true"
    role="menu"
    tabindex="0"
  >
    <li
      class="ant-menu-submenu ant-menu-submenu-vertical ant-menu-submenu-selected"
      role="none"
      style="background: rgba(255, 255, 255, 0.3);"
    >
      <div
        aria-controls="rc-menu-uuid-sub1-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub1"
        role="menuitem"
        tabindex="-1"
      >
        <span
          aria-label="mail"
          class="anticon anticon-mail ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="mail"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation One Long Long Long Long
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var ant-menu-submenu-placement-rightTop"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-vertical"
          data-menu-list="true"
          id="rc-menu-uuid-sub1-popup"
          role="menu"
        >
          <li
            class="ant-menu-item ant-menu-item-selected ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-1"
            role="menuitem"
            style="text-decoration: underline;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 1
            </span>
          </li>
          <li
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-2"
            role="menuitem"
            style="text-decoration: underline;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 2
            </span>
          </li>
          <li
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-3"
            role="menuitem"
            style="text-decoration: underline;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 3
            </span>
          </li>
          <li
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-4"
            role="menuitem"
            style="text-decoration: underline;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 4
            </span>
          </li>
        </ul>
      </div>
    </li>
    <li
      class="ant-menu-submenu ant-menu-submenu-vertical"
      role="none"
      style="background: rgba(255, 255, 255, 0.3);"
    >
      <div
        aria-controls="rc-menu-uuid-sub2-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub2"
        role="menuitem"
        tabindex="-1"
      >
        <span
          aria-label="appstore"
          class="anticon anticon-appstore ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="appstore"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Two
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var ant-menu-submenu-placement-rightTop"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-vertical"
          data-menu-list="true"
          id="rc-menu-uuid-sub2-popup"
          role="menu"
        >
          <li
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-5"
            role="menuitem"
            style="text-decoration: underline;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 5
            </span>
          </li>
          <li
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-6"
            role="menuitem"
            style="text-decoration: underline;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 6
            </span>
          </li>
          <li
            class="ant-menu-submenu ant-menu-submenu-vertical"
            role="none"
            style="background: rgba(255, 255, 255, 0.3);"
          >
            <div
              aria-controls="rc-menu-uuid-sub3-popup"
              aria-expanded="false"
              aria-haspopup="true"
              class="ant-menu-submenu-title"
              data-menu-id="rc-menu-uuid-sub3"
              role="menuitem"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Submenu
              </span>
              <i
                class="ant-menu-submenu-arrow"
              />
            </div>
            <div
              class="ant-menu-submenu ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var ant-menu-submenu-placement-rightTop"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <ul
                class="ant-menu ant-menu-sub ant-menu-vertical"
                data-menu-list="true"
                id="rc-menu-uuid-sub3-popup"
                role="menu"
              >
                <li
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-7"
                  role="menuitem"
                  style="text-decoration: underline;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 7
                  </span>
                </li>
                <li
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-8"
                  role="menuitem"
                  style="text-decoration: underline;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 8
                  </span>
                </li>
              </ul>
            </div>
          </li>
        </ul>
      </div>
    </li>
    <li
      class="ant-menu-item ant-menu-item-only-child"
      data-menu-id="rc-menu-uuid-11"
      role="menuitem"
      style="text-decoration: underline;"
      tabindex="-1"
    >
      <div
        class="ant-menu-inline-collapsed-noicon"
      >
        O
      </div>
    </li>
    <li
      class="ant-menu-item ant-menu-item-only-child"
      data-menu-id="rc-menu-uuid-12"
      role="menuitem"
      style="text-decoration: underline;"
      tabindex="-1"
    >
      <div
        class="ant-menu-inline-collapsed-noicon"
      >
        O
      </div>
    </li>
  </ul>,
  <div
    aria-hidden="true"
    style="display: none;"
  />,
]
`;

exports[`renders components/menu/demo/style-debug.tsx extend context correctly 2`] = `[]`;

exports[`renders components/menu/demo/submenu-theme.tsx extend context correctly 1`] = `
Array [
  <button
    aria-checked="false"
    class="ant-switch css-var-test-id"
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      >
        Dark
      </span>
      <span
        class="ant-switch-inner-unchecked"
      >
        Light
      </span>
    </span>
  </button>,
  <br />,
  <br />,
  <ul
    class="ant-menu ant-menu-root ant-menu-vertical ant-menu-dark css-var-test-id ant-menu-css-var"
    data-menu-list="true"
    role="menu"
    style="width: 256px;"
    tabindex="0"
  >
    <li
      class="ant-menu-submenu ant-menu-submenu-vertical ant-menu-submenu-open ant-menu-submenu-selected"
      role="none"
      theme="light"
    >
      <div
        aria-controls="rc-menu-uuid-sub1-popup"
        aria-expanded="true"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub1"
        role="menuitem"
        tabindex="-1"
      >
        <span
          aria-label="mail"
          class="anticon anticon-mail ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="mail"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation One
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var ant-menu-submenu-placement-rightTop"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-vertical"
          data-menu-list="true"
          id="rc-menu-uuid-sub1-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-selected ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-1"
            role="menuitem"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 1
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-2"
            role="menuitem"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 2
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-3"
            role="menuitem"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 3
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
        </ul>
      </div>
    </li>
    <li
      aria-describedby="test-id"
      class="ant-menu-item ant-menu-item-only-child"
      data-menu-id="rc-menu-uuid-5"
      role="menuitem"
      tabindex="-1"
    >
      <span
        class="ant-menu-title-content"
      >
        Option 5
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
    <li
      aria-describedby="test-id"
      class="ant-menu-item ant-menu-item-only-child"
      data-menu-id="rc-menu-uuid-6"
      role="menuitem"
      tabindex="-1"
    >
      <span
        class="ant-menu-title-content"
      >
        Option 6
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
  </ul>,
  <div
    aria-hidden="true"
    style="display: none;"
  />,
]
`;

exports[`renders components/menu/demo/submenu-theme.tsx extend context correctly 2`] = `[]`;

exports[`renders components/menu/demo/switch-mode.tsx extend context correctly 1`] = `
Array [
  <button
    aria-checked="false"
    class="ant-switch css-var-test-id"
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      />
      <span
        class="ant-switch-inner-unchecked"
      />
    </span>
  </button>,
   Change Mode,
  <div
    class="ant-divider css-var-test-id ant-divider-vertical ant-divider-rail"
    role="separator"
  />,
  <button
    aria-checked="false"
    class="ant-switch css-var-test-id"
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      />
      <span
        class="ant-switch-inner-unchecked"
      />
    </span>
  </button>,
   Change Style,
  <br />,
  <br />,
  <ul
    class="ant-menu ant-menu-root ant-menu-inline ant-menu-light css-var-test-id ant-menu-css-var"
    data-menu-list="true"
    role="menu"
    style="width: 256px;"
    tabindex="0"
  >
    <li
      aria-describedby="test-id"
      class="ant-menu-item ant-menu-item-selected"
      data-menu-id="rc-menu-uuid-1"
      role="menuitem"
      style="padding-left: 24px;"
      tabindex="-1"
    >
      <span
        aria-label="mail"
        class="anticon anticon-mail ant-menu-item-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="mail"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"
          />
        </svg>
      </span>
      <span
        class="ant-menu-title-content"
      >
        Navigation One
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
    <li
      aria-describedby="test-id"
      class="ant-menu-item"
      data-menu-id="rc-menu-uuid-2"
      role="menuitem"
      style="padding-left: 24px;"
      tabindex="-1"
    >
      <span
        aria-label="calendar"
        class="anticon anticon-calendar ant-menu-item-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="calendar"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
          />
        </svg>
      </span>
      <span
        class="ant-menu-title-content"
      >
        Navigation Two
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
    <li
      class="ant-menu-submenu ant-menu-submenu-inline ant-menu-submenu-open"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-sub1-popup"
        aria-expanded="true"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub1"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="appstore"
          class="anticon anticon-appstore ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="appstore"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Two
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-sub1-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-3"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 3
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-4"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 4
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            class="ant-menu-submenu ant-menu-submenu-inline"
            role="none"
          >
            <div
              aria-controls="rc-menu-uuid-sub1-2-popup"
              aria-expanded="false"
              aria-haspopup="true"
              class="ant-menu-submenu-title"
              data-menu-id="rc-menu-uuid-sub1-2"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Submenu
              </span>
              <i
                class="ant-menu-submenu-arrow"
              />
            </div>
            <div
              class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <ul
                class="ant-menu ant-menu-sub ant-menu-inline"
                data-menu-list="true"
                id="rc-menu-uuid-sub1-2-popup"
                role="menu"
              >
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-5"
                  role="menuitem"
                  style="padding-left: 72px;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 5
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-6"
                  role="menuitem"
                  style="padding-left: 72px;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 6
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
              </ul>
            </div>
          </li>
        </ul>
      </div>
      <ul
        class="ant-menu ant-menu-sub ant-menu-inline"
        data-menu-list="true"
        id="rc-menu-uuid-sub1-popup"
        role="menu"
      >
        <li
          aria-describedby="test-id"
          class="ant-menu-item ant-menu-item-only-child"
          data-menu-id="rc-menu-uuid-3"
          role="menuitem"
          style="padding-left: 48px;"
          tabindex="-1"
        >
          <span
            class="ant-menu-title-content"
          >
            Option 3
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
        <li
          aria-describedby="test-id"
          class="ant-menu-item ant-menu-item-only-child"
          data-menu-id="rc-menu-uuid-4"
          role="menuitem"
          style="padding-left: 48px;"
          tabindex="-1"
        >
          <span
            class="ant-menu-title-content"
          >
            Option 4
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
        <li
          class="ant-menu-submenu ant-menu-submenu-inline"
          role="none"
        >
          <div
            aria-controls="rc-menu-uuid-sub1-2-popup"
            aria-expanded="false"
            aria-haspopup="true"
            class="ant-menu-submenu-title"
            data-menu-id="rc-menu-uuid-sub1-2"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Submenu
            </span>
            <i
              class="ant-menu-submenu-arrow"
            />
          </div>
          <div
            class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <ul
              class="ant-menu ant-menu-sub ant-menu-inline"
              data-menu-list="true"
              id="rc-menu-uuid-sub1-2-popup"
              role="menu"
            >
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-5"
                role="menuitem"
                style="padding-left: 72px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 5
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-6"
                role="menuitem"
                style="padding-left: 72px;"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 6
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
            </ul>
          </div>
        </li>
      </ul>
    </li>
    <li
      class="ant-menu-submenu ant-menu-submenu-inline"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-sub2-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub2"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="setting"
          class="anticon anticon-setting ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="setting"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Three
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-sub2-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-7"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 7
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-8"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 8
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-9"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 9
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-10"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 10
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
        </ul>
      </div>
    </li>
    <li
      aria-describedby="test-id"
      class="ant-menu-item"
      data-menu-id="rc-menu-uuid-link"
      role="menuitem"
      style="padding-left: 24px;"
      tabindex="-1"
    >
      <span
        aria-label="link"
        class="anticon anticon-link ant-menu-item-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="link"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M574 665.4a8.03 8.03 0 00-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 00-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 000 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 000 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 00-11.3 0L372.3 598.7a8.03 8.03 0 000 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z"
          />
        </svg>
      </span>
      <span
        class="ant-menu-title-content"
      >
        <a
          href="https://ant.design"
          rel="noopener noreferrer"
          target="_blank"
        >
          Ant Design
        </a>
      </span>
    </li>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        />
      </div>
    </div>
  </ul>,
  <div
    aria-hidden="true"
    style="display: none;"
  />,
]
`;

exports[`renders components/menu/demo/switch-mode.tsx extend context correctly 2`] = `[]`;

exports[`renders components/menu/demo/theme.tsx extend context correctly 1`] = `
Array [
  <button
    aria-checked="true"
    class="ant-switch css-var-test-id ant-switch-checked"
    role="switch"
    type="button"
  >
    <div
      class="ant-switch-handle"
    />
    <span
      class="ant-switch-inner"
    >
      <span
        class="ant-switch-inner-checked"
      >
        Dark
      </span>
      <span
        class="ant-switch-inner-unchecked"
      >
        Light
      </span>
    </span>
  </button>,
  <br />,
  <br />,
  <ul
    class="ant-menu ant-menu-root ant-menu-inline ant-menu-dark css-var-test-id ant-menu-css-var"
    data-menu-list="true"
    role="menu"
    style="width: 256px;"
    tabindex="0"
  >
    <li
      class="ant-menu-submenu ant-menu-submenu-inline ant-menu-submenu-open ant-menu-submenu-selected"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-sub1-popup"
        aria-expanded="true"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub1"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="mail"
          class="anticon anticon-mail ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="mail"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation One
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-sub1-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-selected ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-1"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 1
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-2"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 2
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-3"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 3
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-4"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 4
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
        </ul>
      </div>
      <ul
        class="ant-menu ant-menu-sub ant-menu-inline"
        data-menu-list="true"
        id="rc-menu-uuid-sub1-popup"
        role="menu"
      >
        <li
          aria-describedby="test-id"
          class="ant-menu-item ant-menu-item-selected ant-menu-item-only-child"
          data-menu-id="rc-menu-uuid-1"
          role="menuitem"
          style="padding-left: 48px;"
          tabindex="-1"
        >
          <span
            class="ant-menu-title-content"
          >
            Option 1
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
        <li
          aria-describedby="test-id"
          class="ant-menu-item ant-menu-item-only-child"
          data-menu-id="rc-menu-uuid-2"
          role="menuitem"
          style="padding-left: 48px;"
          tabindex="-1"
        >
          <span
            class="ant-menu-title-content"
          >
            Option 2
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
        <li
          aria-describedby="test-id"
          class="ant-menu-item ant-menu-item-only-child"
          data-menu-id="rc-menu-uuid-3"
          role="menuitem"
          style="padding-left: 48px;"
          tabindex="-1"
        >
          <span
            class="ant-menu-title-content"
          >
            Option 3
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
        <li
          aria-describedby="test-id"
          class="ant-menu-item ant-menu-item-only-child"
          data-menu-id="rc-menu-uuid-4"
          role="menuitem"
          style="padding-left: 48px;"
          tabindex="-1"
        >
          <span
            class="ant-menu-title-content"
          >
            Option 4
          </span>
        </li>
        <div
          class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-tooltip-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-tooltip-content"
          >
            <div
              class="ant-tooltip-inner"
              id="test-id"
              role="tooltip"
            />
          </div>
        </div>
      </ul>
    </li>
    <li
      class="ant-menu-submenu ant-menu-submenu-inline"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-sub2-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub2"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="appstore"
          class="anticon anticon-appstore ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="appstore"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Two
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-sub2-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-5"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 5
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-6"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 6
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            class="ant-menu-submenu ant-menu-submenu-inline"
            role="none"
          >
            <div
              aria-controls="rc-menu-uuid-sub3-popup"
              aria-expanded="false"
              aria-haspopup="true"
              class="ant-menu-submenu-title"
              data-menu-id="rc-menu-uuid-sub3"
              role="menuitem"
              style="padding-left: 48px;"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Submenu
              </span>
              <i
                class="ant-menu-submenu-arrow"
              />
            </div>
            <div
              class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <ul
                class="ant-menu ant-menu-sub ant-menu-inline"
                data-menu-list="true"
                id="rc-menu-uuid-sub3-popup"
                role="menu"
              >
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-7"
                  role="menuitem"
                  style="padding-left: 72px;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 7
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-8"
                  role="menuitem"
                  style="padding-left: 72px;"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 8
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
              </ul>
            </div>
          </li>
        </ul>
      </div>
    </li>
    <li
      class="ant-menu-submenu ant-menu-submenu-inline"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-sub4-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub4"
        role="menuitem"
        style="padding-left: 24px;"
        tabindex="-1"
      >
        <span
          aria-label="setting"
          class="anticon anticon-setting ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="setting"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Three
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-menu-submenu-popup ant-menu ant-menu-dark css-var-test-id ant-menu-css-var"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-inline"
          data-menu-list="true"
          id="rc-menu-uuid-sub4-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-9"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 9
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-10"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 10
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-11"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 11
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-12"
            role="menuitem"
            style="padding-left: 48px;"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 12
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
        </ul>
      </div>
    </li>
  </ul>,
  <div
    aria-hidden="true"
    style="display: none;"
  />,
]
`;

exports[`renders components/menu/demo/theme.tsx extend context correctly 2`] = `[]`;

exports[`renders components/menu/demo/vertical.tsx extend context correctly 1`] = `
Array [
  <ul
    class="ant-menu ant-menu-root ant-menu-vertical ant-menu-light css-var-test-id ant-menu-css-var"
    data-menu-list="true"
    role="menu"
    style="width: 256px;"
    tabindex="0"
  >
    <li
      class="ant-menu-submenu ant-menu-submenu-vertical"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-sub1-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub1"
        role="menuitem"
        tabindex="-1"
      >
        <span
          aria-label="mail"
          class="anticon anticon-mail ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="mail"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation One
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var ant-menu-submenu-placement-rightTop"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-vertical"
          data-menu-list="true"
          id="rc-menu-uuid-sub1-popup"
          role="menu"
        >
          <li
            class="ant-menu-item-group"
            role="presentation"
          >
            <div
              class="ant-menu-item-group-title"
              role="presentation"
              title="Item 1"
            >
              Item 1
            </div>
            <ul
              class="ant-menu-item-group-list"
              role="group"
            >
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-1"
                role="menuitem"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 1
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-2"
                role="menuitem"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 2
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
            </ul>
          </li>
          <li
            class="ant-menu-item-group"
            role="presentation"
          >
            <div
              class="ant-menu-item-group-title"
              role="presentation"
              title="Item 2"
            >
              Item 2
            </div>
            <ul
              class="ant-menu-item-group-list"
              role="group"
            >
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-3"
                role="menuitem"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 3
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
              <li
                aria-describedby="test-id"
                class="ant-menu-item ant-menu-item-only-child"
                data-menu-id="rc-menu-uuid-4"
                role="menuitem"
                tabindex="-1"
              >
                <span
                  class="ant-menu-title-content"
                >
                  Option 4
                </span>
              </li>
              <div
                class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
              >
                <div
                  class="ant-tooltip-arrow"
                  style="position: absolute; top: 0px; left: 0px;"
                />
                <div
                  class="ant-tooltip-content"
                >
                  <div
                    class="ant-tooltip-inner"
                    id="test-id"
                    role="tooltip"
                  />
                </div>
              </div>
            </ul>
          </li>
        </ul>
      </div>
    </li>
    <li
      class="ant-menu-submenu ant-menu-submenu-vertical"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-sub2-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub2"
        role="menuitem"
        tabindex="-1"
      >
        <span
          aria-label="appstore"
          class="anticon anticon-appstore ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="appstore"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Two
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var ant-menu-submenu-placement-rightTop"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-vertical"
          data-menu-list="true"
          id="rc-menu-uuid-sub2-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-5"
            role="menuitem"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 5
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-6"
            role="menuitem"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 6
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            class="ant-menu-submenu ant-menu-submenu-vertical"
            role="none"
          >
            <div
              aria-controls="rc-menu-uuid-sub3-popup"
              aria-expanded="false"
              aria-haspopup="true"
              class="ant-menu-submenu-title"
              data-menu-id="rc-menu-uuid-sub3"
              role="menuitem"
              tabindex="-1"
            >
              <span
                class="ant-menu-title-content"
              >
                Submenu
              </span>
              <i
                class="ant-menu-submenu-arrow"
              />
            </div>
            <div
              class="ant-menu-submenu ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var ant-menu-submenu-placement-rightTop"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <ul
                class="ant-menu ant-menu-sub ant-menu-vertical"
                data-menu-list="true"
                id="rc-menu-uuid-sub3-popup"
                role="menu"
              >
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-7"
                  role="menuitem"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 7
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
                <li
                  aria-describedby="test-id"
                  class="ant-menu-item ant-menu-item-only-child"
                  data-menu-id="rc-menu-uuid-8"
                  role="menuitem"
                  tabindex="-1"
                >
                  <span
                    class="ant-menu-title-content"
                  >
                    Option 8
                  </span>
                </li>
                <div
                  class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
                  style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
                >
                  <div
                    class="ant-tooltip-arrow"
                    style="position: absolute; top: 0px; left: 0px;"
                  />
                  <div
                    class="ant-tooltip-content"
                  >
                    <div
                      class="ant-tooltip-inner"
                      id="test-id"
                      role="tooltip"
                    />
                  </div>
                </div>
              </ul>
            </div>
          </li>
        </ul>
      </div>
    </li>
    <li
      class="ant-menu-submenu ant-menu-submenu-vertical"
      role="none"
    >
      <div
        aria-controls="rc-menu-uuid-sub4-popup"
        aria-expanded="false"
        aria-haspopup="true"
        class="ant-menu-submenu-title"
        data-menu-id="rc-menu-uuid-sub4"
        role="menuitem"
        tabindex="-1"
      >
        <span
          aria-label="setting"
          class="anticon anticon-setting ant-menu-item-icon"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="setting"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
            />
          </svg>
        </span>
        <span
          class="ant-menu-title-content"
        >
          Navigation Three
        </span>
        <i
          class="ant-menu-submenu-arrow"
        />
      </div>
      <div
        class="ant-menu-submenu ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-menu-submenu-popup ant-menu ant-menu-light css-var-test-id ant-menu-css-var ant-menu-submenu-placement-rightTop"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          class="ant-menu ant-menu-sub ant-menu-vertical"
          data-menu-list="true"
          id="rc-menu-uuid-sub4-popup"
          role="menu"
        >
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-9"
            role="menuitem"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 9
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-10"
            role="menuitem"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 10
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-11"
            role="menuitem"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 11
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
          <li
            aria-describedby="test-id"
            class="ant-menu-item ant-menu-item-only-child"
            data-menu-id="rc-menu-uuid-12"
            role="menuitem"
            tabindex="-1"
          >
            <span
              class="ant-menu-title-content"
            >
              Option 12
            </span>
          </li>
          <div
            class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast css-var-test-id ant-menu-inline-collapsed-tooltip ant-tooltip-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
          >
            <div
              class="ant-tooltip-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-tooltip-content"
            >
              <div
                class="ant-tooltip-inner"
                id="test-id"
                role="tooltip"
              />
            </div>
          </div>
        </ul>
      </div>
    </li>
  </ul>,
  <div
    aria-hidden="true"
    style="display: none;"
  />,
]
`;

exports[`renders components/menu/demo/vertical.tsx extend context correctly 2`] = `[]`;
