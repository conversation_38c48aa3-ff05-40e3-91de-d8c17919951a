// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/divider/demo/component-token.tsx extend context correctly 1`] = `
Array [
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-center"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Text
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Left Text
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-end"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Right Text
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
      style="margin: 0px;"
    >
      Left Text margin with 0
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-end"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
      style="margin: 0px 50px;"
    >
      Right Text margin with 50px
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
]
`;

exports[`renders components/divider/demo/component-token.tsx extend context correctly 2`] = `[]`;

exports[`renders components/divider/demo/customize-style.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
    style="border-width: 2px; border-color: rgb(124, 179, 5);"
  />,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-dashed ant-divider-rail"
    role="separator"
    style="border-color: rgb(124, 179, 5);"
  />,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-center ant-divider-dashed"
    role="separator"
    style="border-color: rgb(124, 179, 5);"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Text
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-vertical ant-divider-rail"
    role="separator"
    style="height: 60px; border-color: rgb(124, 179, 5);"
  />,
  <div
    class="ant-divider css-var-test-id ant-divider-vertical ant-divider-dashed ant-divider-rail"
    role="separator"
    style="height: 60px; border-color: rgb(124, 179, 5);"
  />,
  <div
    style="display: flex; flex-direction: column; height: 50px; box-shadow: 0 0 1px red;"
  >
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
      role="separator"
      style="background: rgba(0, 255, 0, 0.05);"
    >
      <div
        class="ant-divider-rail ant-divider-rail-start"
      />
      <span
        class="ant-divider-inner-text"
      >
        Text
      </span>
      <div
        class="ant-divider-rail ant-divider-rail-end"
      />
    </div>
  </div>,
]
`;

exports[`renders components/divider/demo/customize-style.tsx extend context correctly 2`] = `[]`;

exports[`renders components/divider/demo/horizontal.tsx extend context correctly 1`] = `
Array [
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
  />,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-dashed ant-divider-rail"
    role="separator"
  />,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
]
`;

exports[`renders components/divider/demo/horizontal.tsx extend context correctly 2`] = `[]`;

exports[`renders components/divider/demo/plain.tsx extend context correctly 1`] = `
Array [
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-center ant-divider-plain"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Text
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start ant-divider-plain"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Left Text
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-end ant-divider-plain"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Right Text
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
]
`;

exports[`renders components/divider/demo/plain.tsx extend context correctly 2`] = `[]`;

exports[`renders components/divider/demo/size.tsx extend context correctly 1`] = `
Array [
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-sm ant-divider-rail"
    role="separator"
  />,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-md ant-divider-rail"
    role="separator"
  />,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
  />,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
]
`;

exports[`renders components/divider/demo/size.tsx extend context correctly 2`] = `[]`;

exports[`renders components/divider/demo/style-class.tsx extend context correctly 1`] = `
<div>
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-center demo-divider-root"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start demo-divider-rail"
    />
    <span
      class="ant-divider-inner-text demo-divider-content"
    >
      classNames Object
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end demo-divider-rail"
    />
  </div>
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start demo-divider-root--start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      classNames Function
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-center"
    role="separator"
    style="border-width: 2px; border-style: dashed;"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
      style="opacity: 0.85;"
    />
    <span
      class="ant-divider-inner-text"
      style="font-style: italic;"
    >
      styles Object
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
      style="opacity: 0.85;"
    />
  </div>
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-center ant-divider-sm"
    role="separator"
    style="opacity: 0.6; cursor: default;"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      styles Function
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>
</div>
`;

exports[`renders components/divider/demo/style-class.tsx extend context correctly 2`] = `[]`;

exports[`renders components/divider/demo/variant.tsx extend context correctly 1`] = `
Array [
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-center"
    role="separator"
    style="border-color: rgb(124, 179, 5);"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Solid
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-center ant-divider-dotted"
    role="separator"
    style="border-color: rgb(124, 179, 5);"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Dotted
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-center ant-divider-dashed"
    role="separator"
    style="border-color: rgb(124, 179, 5);"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Dashed
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
]
`;

exports[`renders components/divider/demo/variant.tsx extend context correctly 2`] = `[]`;

exports[`renders components/divider/demo/vertical.tsx extend context correctly 1`] = `
Array [
  Text,
  <div
    class="ant-divider css-var-test-id ant-divider-vertical ant-divider-rail"
    role="separator"
  />,
  <a
    href="#"
  >
    Link
  </a>,
  <div
    class="ant-divider css-var-test-id ant-divider-vertical ant-divider-rail"
    role="separator"
  />,
  <a
    href="#"
  >
    Link
  </a>,
]
`;

exports[`renders components/divider/demo/vertical.tsx extend context correctly 2`] = `[]`;

exports[`renders components/divider/demo/with-text.tsx extend context correctly 1`] = `
Array [
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-center"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Text
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Left Text
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-end"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Right Text
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
      style="margin: 0px;"
    >
      Left Text margin with 0
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-end"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
      style="margin: 0px 50px;"
    >
      Right Text margin with 50px
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <p>
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed nonne merninisti licere mihi ista probare, quae sunt a te dicta? Refert tamen, quo modo.
  </p>,
]
`;

exports[`renders components/divider/demo/with-text.tsx extend context correctly 2`] = `[]`;
