// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/flex/demo/align.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-start ant-flex-gap-middle ant-flex-vertical"
>
  <p>
    Select justify :
  </p>
  <div
    aria-label="segmented control"
    class="ant-segmented css-var-test-id"
    role="radiogroup"
    tabindex="0"
  >
    <div
      class="ant-segmented-group"
    >
      <label
        class="ant-segmented-item ant-segmented-item-selected"
      >
        <input
          checked=""
          class="ant-segmented-item-input"
          name="test-id"
          type="radio"
        />
        <div
          aria-checked="true"
          class="ant-segmented-item-label"
          role="radio"
          title="flex-start"
        >
          flex-start
        </div>
      </label>
      <label
        class="ant-segmented-item"
      >
        <input
          class="ant-segmented-item-input"
          name="test-id"
          type="radio"
        />
        <div
          aria-checked="false"
          class="ant-segmented-item-label"
          role="radio"
          title="center"
        >
          center
        </div>
      </label>
      <label
        class="ant-segmented-item"
      >
        <input
          class="ant-segmented-item-input"
          name="test-id"
          type="radio"
        />
        <div
          aria-checked="false"
          class="ant-segmented-item-label"
          role="radio"
          title="flex-end"
        >
          flex-end
        </div>
      </label>
      <label
        class="ant-segmented-item"
      >
        <input
          class="ant-segmented-item-input"
          name="test-id"
          type="radio"
        />
        <div
          aria-checked="false"
          class="ant-segmented-item-label"
          role="radio"
          title="space-between"
        >
          space-between
        </div>
      </label>
      <label
        class="ant-segmented-item"
      >
        <input
          class="ant-segmented-item-input"
          name="test-id"
          type="radio"
        />
        <div
          aria-checked="false"
          class="ant-segmented-item-label"
          role="radio"
          title="space-around"
        >
          space-around
        </div>
      </label>
      <label
        class="ant-segmented-item"
      >
        <input
          class="ant-segmented-item-input"
          name="test-id"
          type="radio"
        />
        <div
          aria-checked="false"
          class="ant-segmented-item-label"
          role="radio"
          title="space-evenly"
        >
          space-evenly
        </div>
      </label>
    </div>
  </div>
  <p>
    Select align :
  </p>
  <div
    aria-label="segmented control"
    class="ant-segmented css-var-test-id"
    role="radiogroup"
    tabindex="0"
  >
    <div
      class="ant-segmented-group"
    >
      <label
        class="ant-segmented-item ant-segmented-item-selected"
      >
        <input
          checked=""
          class="ant-segmented-item-input"
          name="test-id"
          type="radio"
        />
        <div
          aria-checked="true"
          class="ant-segmented-item-label"
          role="radio"
          title="flex-start"
        >
          flex-start
        </div>
      </label>
      <label
        class="ant-segmented-item"
      >
        <input
          class="ant-segmented-item-input"
          name="test-id"
          type="radio"
        />
        <div
          aria-checked="false"
          class="ant-segmented-item-label"
          role="radio"
          title="center"
        >
          center
        </div>
      </label>
      <label
        class="ant-segmented-item"
      >
        <input
          class="ant-segmented-item-input"
          name="test-id"
          type="radio"
        />
        <div
          aria-checked="false"
          class="ant-segmented-item-label"
          role="radio"
          title="flex-end"
        >
          flex-end
        </div>
      </label>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-align-flex-start ant-flex-justify-flex-start"
    style="width:100%;height:120px;border-radius:6px;border:1px solid #40a9ff"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Primary
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Primary
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Primary
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Primary
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/flex/demo/basic.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-wrapper ant-radio-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-checked"
      >
        <input
          checked=""
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="horizontal"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        horizontal
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="vertical"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        vertical
      </span>
    </label>
  </div>
  <div
    class="ant-flex css-var-test-id"
  >
    <div
      style="width:25%;height:54px;background-color:#1677ffbf"
    />
    <div
      style="width:25%;height:54px;background-color:#1677ff"
    />
    <div
      style="width:25%;height:54px;background-color:#1677ffbf"
    />
    <div
      style="width:25%;height:54px;background-color:#1677ff"
    />
  </div>
</div>
`;

exports[`renders components/flex/demo/combination.tsx correctly 1`] = `
<div
  class="ant-card ant-card-bordered ant-card-hoverable css-var-test-id"
  style="width:620px"
>
  <div
    class="ant-card-body"
    style="padding:0;overflow:hidden"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-justify-space-between"
    >
      <img
        alt="avatar"
        draggable="false"
        src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
        style="display:block;width:273px"
      />
      <div
        class="ant-flex css-var-test-id ant-flex-align-flex-end ant-flex-justify-space-between ant-flex-vertical"
        style="padding:32px"
      >
        <h3
          class="ant-typography css-var-test-id"
        >
          “antd is an enterprise-class UI design language and React UI library.”
        </h3>
        <a
          aria-disabled="false"
          class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
          href="https://ant.design"
          tabindex="0"
          target="_blank"
        >
          <span>
            Get Started
          </span>
        </a>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/flex/demo/debug.tsx correctly 1`] = `
Array [
  <div
    class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-vertical"
  >
    <div
      style="height:60px;background-color:#1677ffbf"
    />
    <div
      style="height:60px;background-color:#1677ff"
    />
    <div
      style="height:60px;background-color:#1677ffbf"
    />
    <div
      style="height:60px;background-color:#1677ff"
    />
  </div>,
  <div
    class="ant-flex css-var-test-id"
    style="margin-top:20px"
  >
    <div
      style="width:25%;height:40px;background-color:#1677ffbf"
    />
    <div
      style="width:25%;height:60px;background-color:#1677ff"
    />
    <div
      style="width:25%;height:40px;background-color:#1677ffbf"
    />
    <div
      style="width:25%;height:60px;background-color:#1677ff"
    />
  </div>,
]
`;

exports[`renders components/flex/demo/gap.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <div
    class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
  >
    <label
      class="ant-radio-wrapper ant-radio-wrapper-checked css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-checked"
      >
        <input
          checked=""
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="small"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        small
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="middle"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        middle
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="large"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        large
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          name="test-id"
          type="radio"
          value="customize"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span
        class="ant-radio-label"
      >
        customize
      </span>
    </label>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-gap-small"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Primary
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Default
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed"
      type="button"
    >
      <span>
        Dashed
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-link ant-btn-color-link ant-btn-variant-link"
      type="button"
    >
      <span>
        Link
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/flex/demo/wrap.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
  <button
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>
</div>
`;
