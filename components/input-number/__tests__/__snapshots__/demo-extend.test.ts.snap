// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/input-number/demo/addon.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number-group-wrapper ant-input-number-group-wrapper-outlined css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-wrapper ant-input-number-group"
      >
        <div
          class="ant-input-number-group-addon"
        >
          +
        </div>
        <div
          class="ant-input-number ant-input-number-outlined"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="false"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              aria-valuenow="100"
              autocomplete="off"
              class="ant-input-number-input"
              role="spinbutton"
              step="1"
              value="100"
            />
          </div>
        </div>
        <div
          class="ant-input-number-group-addon"
        >
          $
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number-group-wrapper ant-input-number-group-wrapper-outlined css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-wrapper ant-input-number-group"
      >
        <div
          class="ant-input-number-group-addon"
        >
          <div
            class="ant-select ant-select-outlined css-var-test-id ant-select-css-var ant-select-single ant-select-show-arrow"
            style="width: 60px;"
          >
            <div
              class="ant-select-selector"
            >
              <span
                class="ant-select-selection-wrap"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="test-id_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="test-id_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="test-id"
                    readonly=""
                    role="combobox"
                    style="opacity: 0;"
                    type="search"
                    unselectable="on"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-item"
                  title="+"
                >
                  +
                </span>
              </span>
            </div>
            <div
              class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-placement-bottomLeft"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div>
                <div
                  id="test-id_list"
                  role="listbox"
                  style="height: 0px; width: 0px; overflow: hidden;"
                >
                  <div
                    aria-label="+"
                    aria-selected="true"
                    id="test-id_list_0"
                    role="option"
                  >
                    add
                  </div>
                  <div
                    aria-label="-"
                    aria-selected="false"
                    id="test-id_list_1"
                    role="option"
                  >
                    minus
                  </div>
                </div>
                <div
                  class="rc-virtual-list"
                  style="position: relative;"
                >
                  <div
                    class="rc-virtual-list-holder"
                    style="max-height: 256px; overflow-y: auto; overflow-anchor: none;"
                  >
                    <div>
                      <div
                        class="rc-virtual-list-holder-inner"
                        style="display: flex; flex-direction: column;"
                      >
                        <div
                          class="ant-select-item ant-select-item-option ant-select-item-option-active ant-select-item-option-selected"
                          title="+"
                        >
                          <div
                            class="ant-select-item-option-content"
                          >
                            +
                          </div>
                          <span
                            aria-hidden="true"
                            class="ant-select-item-option-state"
                            style="user-select: none;"
                            unselectable="on"
                          />
                        </div>
                        <div
                          class="ant-select-item ant-select-item-option"
                          title="-"
                        >
                          <div
                            class="ant-select-item-option-content"
                          >
                            -
                          </div>
                          <span
                            aria-hidden="true"
                            class="ant-select-item-option-state"
                            style="user-select: none;"
                            unselectable="on"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <span
              aria-hidden="true"
              class="ant-select-arrow"
              style="user-select: none;"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-select-suffix"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
        </div>
        <div
          class="ant-input-number ant-input-number-outlined"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="false"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              aria-valuenow="100"
              autocomplete="off"
              class="ant-input-number-input"
              role="spinbutton"
              step="1"
              value="100"
            />
          </div>
        </div>
        <div
          class="ant-input-number-group-addon"
        >
          <div
            class="ant-select ant-select-outlined css-var-test-id ant-select-css-var ant-select-single ant-select-show-arrow"
            style="width: 60px;"
          >
            <div
              class="ant-select-selector"
            >
              <span
                class="ant-select-selection-wrap"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="test-id_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="test-id_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="test-id"
                    readonly=""
                    role="combobox"
                    style="opacity: 0;"
                    type="search"
                    unselectable="on"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-item"
                  title="$"
                >
                  $
                </span>
              </span>
            </div>
            <div
              class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-placement-bottomLeft"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div>
                <div
                  id="test-id_list"
                  role="listbox"
                  style="height: 0px; width: 0px; overflow: hidden;"
                >
                  <div
                    aria-label="$"
                    aria-selected="true"
                    id="test-id_list_0"
                    role="option"
                  >
                    USD
                  </div>
                  <div
                    aria-label="€"
                    aria-selected="false"
                    id="test-id_list_1"
                    role="option"
                  >
                    EUR
                  </div>
                </div>
                <div
                  class="rc-virtual-list"
                  style="position: relative;"
                >
                  <div
                    class="rc-virtual-list-holder"
                    style="max-height: 256px; overflow-y: auto; overflow-anchor: none;"
                  >
                    <div>
                      <div
                        class="rc-virtual-list-holder-inner"
                        style="display: flex; flex-direction: column;"
                      >
                        <div
                          class="ant-select-item ant-select-item-option ant-select-item-option-active ant-select-item-option-selected"
                          title="$"
                        >
                          <div
                            class="ant-select-item-option-content"
                          >
                            $
                          </div>
                          <span
                            aria-hidden="true"
                            class="ant-select-item-option-state"
                            style="user-select: none;"
                            unselectable="on"
                          />
                        </div>
                        <div
                          class="ant-select-item ant-select-item-option"
                          title="€"
                        >
                          <div
                            class="ant-select-item-option-content"
                          >
                            €
                          </div>
                          <span
                            aria-hidden="true"
                            class="ant-select-item-option-state"
                            style="user-select: none;"
                            unselectable="on"
                          />
                        </div>
                        <div
                          class="ant-select-item ant-select-item-option"
                          title="£"
                        >
                          <div
                            class="ant-select-item-option-content"
                          >
                            £
                          </div>
                          <span
                            aria-hidden="true"
                            class="ant-select-item-option-state"
                            style="user-select: none;"
                            unselectable="on"
                          />
                        </div>
                        <div
                          class="ant-select-item ant-select-item-option"
                          title="¥"
                        >
                          <div
                            class="ant-select-item-option-content"
                          >
                            ¥
                          </div>
                          <span
                            aria-hidden="true"
                            class="ant-select-item-option-state"
                            style="user-select: none;"
                            unselectable="on"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <span
              aria-hidden="true"
              class="ant-select-arrow"
              style="user-select: none;"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-select-suffix"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number-group-wrapper ant-input-number-group-wrapper-outlined css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-wrapper ant-input-number-group"
      >
        <div
          class="ant-input-number ant-input-number-outlined"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="false"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              aria-valuenow="100"
              autocomplete="off"
              class="ant-input-number-input"
              role="spinbutton"
              step="1"
              value="100"
            />
          </div>
        </div>
        <div
          class="ant-input-number-group-addon"
        >
          <span
            aria-label="setting"
            class="anticon anticon-setting"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="setting"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
              />
            </svg>
          </span>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number-group-wrapper ant-input-number-group-wrapper-outlined css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-wrapper ant-input-number-group"
      >
        <div
          class="ant-input-number-group-addon"
        >
          <div
            class="ant-select ant-cascader ant-select-outlined ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-single ant-select-allow-clear ant-select-show-arrow"
            style="width: 150px;"
          >
            <div
              class="ant-select-selector"
            >
              <span
                class="ant-select-selection-wrap"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="test-id_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="test-id_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="test-id"
                    readonly=""
                    role="combobox"
                    style="opacity: 0;"
                    type="search"
                    unselectable="on"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-placeholder"
                >
                  cascader
                </span>
              </span>
            </div>
            <div
              class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-cascader-dropdown ant-select-css-var ant-cascader-css-var css-var-test-id ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
            >
              <div>
                <div
                  class="ant-cascader-menus ant-cascader-menu-empty"
                >
                  <ul
                    class="ant-cascader-menu"
                    role="menu"
                  >
                    <li
                      aria-checked="false"
                      class="ant-cascader-menu-item ant-cascader-menu-item-disabled"
                      data-path-key="__EMPTY__"
                      role="menuitemcheckbox"
                    >
                      <div
                        class="ant-cascader-menu-item-content"
                      >
                        <div
                          class="css-var-test-id ant-empty ant-empty-normal ant-empty-small"
                        >
                          <div
                            class="ant-empty-image"
                          >
                            <svg
                              height="41"
                              viewBox="0 0 64 41"
                              width="64"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                No data
                              </title>
                              <g
                                fill="none"
                                fill-rule="evenodd"
                                transform="translate(0 1)"
                              >
                                <ellipse
                                  cx="32"
                                  cy="33"
                                  fill="#f5f5f5"
                                  rx="32"
                                  ry="7"
                                />
                                <g
                                  fill-rule="nonzero"
                                  stroke="#d9d9d9"
                                >
                                  <path
                                    d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                                  />
                                  <path
                                    d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                                    fill="#fafafa"
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                          <div
                            class="ant-empty-description"
                          >
                            No data
                          </div>
                        </div>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <span
              aria-hidden="true"
              class="ant-select-arrow"
              style="user-select: none;"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-select-suffix"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
        </div>
        <div
          class="ant-input-number ant-input-number-outlined"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="false"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              aria-valuenow="100"
              autocomplete="off"
              class="ant-input-number-input"
              role="spinbutton"
              step="1"
              value="100"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number-group-wrapper ant-input-number-group-wrapper-disabled ant-input-number-group-wrapper-outlined css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-wrapper ant-input-number-group"
      >
        <div
          class="ant-input-number-group-addon"
        >
          +
        </div>
        <div
          class="ant-input-number ant-input-number-outlined ant-input-number-disabled"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="false"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              aria-valuenow="100"
              autocomplete="off"
              class="ant-input-number-input"
              disabled=""
              role="spinbutton"
              step="1"
              value="100"
            />
          </div>
        </div>
        <div
          class="ant-input-number-group-addon"
        >
          <span
            aria-label="setting"
            class="anticon anticon-setting"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="setting"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
              />
            </svg>
          </span>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number-group-wrapper ant-input-number-group-wrapper-disabled ant-input-number-group-wrapper-outlined css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-wrapper ant-input-number-group"
      >
        <div
          class="ant-input-number-group-addon"
        >
          +
        </div>
        <div
          class="ant-input-number-affix-wrapper ant-input-number-disabled ant-input-number-affix-wrapper-disabled ant-input-number-affix-wrapper-without-controls ant-input-number-outlined"
        >
          <span
            class="ant-input-number-prefix"
          >
            ¥
          </span>
          <div
            class="ant-input-number ant-input-number-disabled"
          >
            <div
              class="ant-input-number-handler-wrap"
            >
              <span
                aria-disabled="false"
                aria-label="Increase Value"
                class="ant-input-number-handler ant-input-number-handler-up"
                role="button"
                unselectable="on"
              >
                <span
                  aria-label="up"
                  class="anticon anticon-up ant-input-number-handler-up-inner"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="up"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                    />
                  </svg>
                </span>
              </span>
              <span
                aria-disabled="false"
                aria-label="Decrease Value"
                class="ant-input-number-handler ant-input-number-handler-down"
                role="button"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-input-number-handler-down-inner"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
              </span>
            </div>
            <div
              class="ant-input-number-input-wrap"
            >
              <input
                aria-valuenow="100"
                autocomplete="off"
                class="ant-input-number-input"
                disabled=""
                role="spinbutton"
                step="1"
                value="100"
              />
            </div>
          </div>
        </div>
        <div
          class="ant-input-number-group-addon"
        >
          <span
            aria-label="setting"
            class="anticon anticon-setting"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="setting"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
              />
            </svg>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/input-number/demo/addon.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input-number/demo/basic.tsx extend context correctly 1`] = `
<div
  class="ant-input-number ant-input-number-outlined css-var-test-id ant-input-number-css-var"
>
  <div
    class="ant-input-number-handler-wrap"
  >
    <span
      aria-disabled="false"
      aria-label="Increase Value"
      class="ant-input-number-handler ant-input-number-handler-up"
      role="button"
      unselectable="on"
    >
      <span
        aria-label="up"
        class="anticon anticon-up ant-input-number-handler-up-inner"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="up"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
          />
        </svg>
      </span>
    </span>
    <span
      aria-disabled="false"
      aria-label="Decrease Value"
      class="ant-input-number-handler ant-input-number-handler-down"
      role="button"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-input-number-handler-down-inner"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>
  <div
    class="ant-input-number-input-wrap"
  >
    <input
      aria-valuemax="10"
      aria-valuemin="1"
      aria-valuenow="3"
      autocomplete="off"
      class="ant-input-number-input"
      role="spinbutton"
      step="1"
      value="3"
    />
  </div>
</div>
`;

exports[`renders components/input-number/demo/basic.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input-number/demo/change-on-wheel.tsx extend context correctly 1`] = `
<div
  class="ant-input-number ant-input-number-outlined css-var-test-id ant-input-number-css-var"
>
  <div
    class="ant-input-number-handler-wrap"
  >
    <span
      aria-disabled="false"
      aria-label="Increase Value"
      class="ant-input-number-handler ant-input-number-handler-up"
      role="button"
      unselectable="on"
    >
      <span
        aria-label="up"
        class="anticon anticon-up ant-input-number-handler-up-inner"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="up"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
          />
        </svg>
      </span>
    </span>
    <span
      aria-disabled="false"
      aria-label="Decrease Value"
      class="ant-input-number-handler ant-input-number-handler-down"
      role="button"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-input-number-handler-down-inner"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>
  <div
    class="ant-input-number-input-wrap"
  >
    <input
      aria-valuemax="10"
      aria-valuemin="1"
      aria-valuenow="3"
      autocomplete="off"
      class="ant-input-number-input"
      role="spinbutton"
      step="1"
      value="3"
    />
  </div>
</div>
`;

exports[`renders components/input-number/demo/change-on-wheel.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input-number/demo/controls.tsx extend context correctly 1`] = `
<div
  class="ant-input-number ant-input-number-outlined css-var-test-id ant-input-number-css-var"
>
  <div
    class="ant-input-number-handler-wrap"
  >
    <span
      aria-disabled="false"
      aria-label="Increase Value"
      class="ant-input-number-handler ant-input-number-handler-up"
      role="button"
      unselectable="on"
    >
      <span
        class="ant-input-number-handler-up-inner"
      >
        <span
          aria-label="arrow-up"
          class="anticon anticon-arrow-up"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="arrow-up"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"
            />
          </svg>
        </span>
      </span>
    </span>
    <span
      aria-disabled="false"
      aria-label="Decrease Value"
      class="ant-input-number-handler ant-input-number-handler-down"
      role="button"
      unselectable="on"
    >
      <span
        class="ant-input-number-handler-down-inner"
      >
        <span
          aria-label="arrow-down"
          class="anticon anticon-arrow-down"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="arrow-down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M862 465.3h-81c-4.6 0-9 2-12.1 5.5L550 723.1V160c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v563.1L255.1 470.8c-3-3.5-7.4-5.5-12.1-5.5h-81c-6.8 0-10.5 8.1-6 13.2L487.9 861a31.96 31.96 0 0048.3 0L868 478.5c4.5-5.2.8-13.2-6-13.2z"
            />
          </svg>
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-input-number-input-wrap"
  >
    <input
      autocomplete="off"
      class="ant-input-number-input"
      role="spinbutton"
      step="1"
      value=""
    />
  </div>
</div>
`;

exports[`renders components/input-number/demo/controls.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input-number/demo/digit.tsx extend context correctly 1`] = `
<div
  class="ant-input-number ant-input-number-outlined css-var-test-id ant-input-number-css-var"
  style="width: 200px;"
>
  <div
    class="ant-input-number-handler-wrap"
  >
    <span
      aria-disabled="false"
      aria-label="Increase Value"
      class="ant-input-number-handler ant-input-number-handler-up"
      role="button"
      unselectable="on"
    >
      <span
        aria-label="up"
        class="anticon anticon-up ant-input-number-handler-up-inner"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="up"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
          />
        </svg>
      </span>
    </span>
    <span
      aria-disabled="false"
      aria-label="Decrease Value"
      class="ant-input-number-handler ant-input-number-handler-down"
      role="button"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-input-number-handler-down-inner"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>
  <div
    class="ant-input-number-input-wrap"
  >
    <input
      aria-valuemax="10"
      aria-valuemin="0"
      aria-valuenow="1"
      autocomplete="off"
      class="ant-input-number-input"
      role="spinbutton"
      step="0.00000000000001"
      value="1.00000000000000"
    />
  </div>
</div>
`;

exports[`renders components/input-number/demo/digit.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input-number/demo/disabled.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-input-number ant-input-number-outlined css-var-test-id ant-input-number-css-var ant-input-number-disabled"
  >
    <div
      class="ant-input-number-handler-wrap"
    >
      <span
        aria-disabled="false"
        aria-label="Increase Value"
        class="ant-input-number-handler ant-input-number-handler-up"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="up"
          class="anticon anticon-up ant-input-number-handler-up-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="up"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
            />
          </svg>
        </span>
      </span>
      <span
        aria-disabled="false"
        aria-label="Decrease Value"
        class="ant-input-number-handler ant-input-number-handler-down"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-input-number-handler-down-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-input-number-input-wrap"
    >
      <input
        aria-valuemax="10"
        aria-valuemin="1"
        aria-valuenow="3"
        autocomplete="off"
        class="ant-input-number-input"
        disabled=""
        role="spinbutton"
        step="1"
        value="3"
      />
    </div>
  </div>,
  <div
    style="margin-top: 20px;"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Toggle disabled
      </span>
    </button>
  </div>,
]
`;

exports[`renders components/input-number/demo/disabled.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input-number/demo/filled-debug.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-vertical"
  style="gap: 12px;"
>
  <div
    class="ant-flex css-var-test-id"
    style="gap: 12px;"
  >
    <div
      class="ant-input-number ant-input-number-filled css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="false"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          autocomplete="off"
          class="ant-input-number-input"
          placeholder="Filled"
          role="spinbutton"
          step="1"
          value=""
        />
      </div>
    </div>
    <div
      class="ant-input-number ant-input-number-filled css-var-test-id ant-input-number-css-var ant-input-number-disabled"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="false"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          autocomplete="off"
          class="ant-input-number-input"
          disabled=""
          placeholder="Filled"
          role="spinbutton"
          step="1"
          value=""
        />
      </div>
    </div>
    <div
      class="ant-input-number ant-input-number-filled ant-input-number-status-error css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="false"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          autocomplete="off"
          class="ant-input-number-input"
          placeholder="Filled"
          role="spinbutton"
          step="1"
          value=""
        />
      </div>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id"
    style="gap: 12px;"
  >
    <div
      class="ant-input-number-affix-wrapper ant-input-number-filled css-var-test-id ant-input-number-css-var"
    >
      <span
        class="ant-input-number-prefix"
      >
        $
      </span>
      <div
        class="ant-input-number"
      >
        <div
          class="ant-input-number-handler-wrap"
        >
          <span
            aria-disabled="false"
            aria-label="Increase Value"
            class="ant-input-number-handler ant-input-number-handler-up"
            role="button"
            unselectable="on"
          >
            <span
              aria-label="up"
              class="anticon anticon-up ant-input-number-handler-up-inner"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="up"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                />
              </svg>
            </span>
          </span>
          <span
            aria-disabled="false"
            aria-label="Decrease Value"
            class="ant-input-number-handler ant-input-number-handler-down"
            role="button"
            unselectable="on"
          >
            <span
              aria-label="down"
              class="anticon anticon-down ant-input-number-handler-down-inner"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="down"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                />
              </svg>
            </span>
          </span>
        </div>
        <div
          class="ant-input-number-input-wrap"
        >
          <input
            autocomplete="off"
            class="ant-input-number-input"
            placeholder="Filled"
            role="spinbutton"
            step="1"
            value=""
          />
        </div>
      </div>
    </div>
    <div
      class="ant-input-number-affix-wrapper ant-input-number-disabled ant-input-number-affix-wrapper-disabled ant-input-number-affix-wrapper-without-controls ant-input-number-filled css-var-test-id ant-input-number-css-var"
    >
      <span
        class="ant-input-number-prefix"
      >
        $
      </span>
      <div
        class="ant-input-number ant-input-number-disabled"
      >
        <div
          class="ant-input-number-handler-wrap"
        >
          <span
            aria-disabled="false"
            aria-label="Increase Value"
            class="ant-input-number-handler ant-input-number-handler-up"
            role="button"
            unselectable="on"
          >
            <span
              aria-label="up"
              class="anticon anticon-up ant-input-number-handler-up-inner"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="up"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                />
              </svg>
            </span>
          </span>
          <span
            aria-disabled="false"
            aria-label="Decrease Value"
            class="ant-input-number-handler ant-input-number-handler-down"
            role="button"
            unselectable="on"
          >
            <span
              aria-label="down"
              class="anticon anticon-down ant-input-number-handler-down-inner"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="down"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                />
              </svg>
            </span>
          </span>
        </div>
        <div
          class="ant-input-number-input-wrap"
        >
          <input
            autocomplete="off"
            class="ant-input-number-input"
            disabled=""
            placeholder="Filled"
            role="spinbutton"
            step="1"
            value=""
          />
        </div>
      </div>
    </div>
    <div
      class="ant-input-number-affix-wrapper ant-input-number-filled ant-input-number-status-error css-var-test-id ant-input-number-css-var"
    >
      <span
        class="ant-input-number-prefix"
      >
        $
      </span>
      <div
        class="ant-input-number"
      >
        <div
          class="ant-input-number-handler-wrap"
        >
          <span
            aria-disabled="false"
            aria-label="Increase Value"
            class="ant-input-number-handler ant-input-number-handler-up"
            role="button"
            unselectable="on"
          >
            <span
              aria-label="up"
              class="anticon anticon-up ant-input-number-handler-up-inner"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="up"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                />
              </svg>
            </span>
          </span>
          <span
            aria-disabled="false"
            aria-label="Decrease Value"
            class="ant-input-number-handler ant-input-number-handler-down"
            role="button"
            unselectable="on"
          >
            <span
              aria-label="down"
              class="anticon anticon-down ant-input-number-handler-down-inner"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="down"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                />
              </svg>
            </span>
          </span>
        </div>
        <div
          class="ant-input-number-input-wrap"
        >
          <input
            autocomplete="off"
            class="ant-input-number-input"
            placeholder="Filled"
            role="spinbutton"
            step="1"
            value=""
          />
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id"
    style="gap: 12px;"
  >
    <div
      class="ant-input-number-group-wrapper ant-input-number-group-wrapper-filled css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-wrapper ant-input-number-group"
      >
        <div
          class="ant-input-number-group-addon"
        >
          http://
        </div>
        <div
          class="ant-input-number ant-input-number-filled"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="false"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              autocomplete="off"
              class="ant-input-number-input"
              placeholder="Filled"
              role="spinbutton"
              step="1"
              value=""
            />
          </div>
        </div>
        <div
          class="ant-input-number-group-addon"
        >
          .com
        </div>
      </div>
    </div>
    <div
      class="ant-input-number-group-wrapper ant-input-number-group-wrapper-disabled ant-input-number-group-wrapper-filled css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-wrapper ant-input-number-group"
      >
        <div
          class="ant-input-number-group-addon"
        >
          http://
        </div>
        <div
          class="ant-input-number ant-input-number-filled ant-input-number-disabled"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="false"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              autocomplete="off"
              class="ant-input-number-input"
              disabled=""
              placeholder="Filled"
              role="spinbutton"
              step="1"
              value=""
            />
          </div>
        </div>
        <div
          class="ant-input-number-group-addon"
        >
          .com
        </div>
      </div>
    </div>
    <div
      class="ant-input-number-group-wrapper ant-input-number-group-wrapper-filled ant-input-number-group-wrapper-status-error css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-wrapper ant-input-number-group"
      >
        <div
          class="ant-input-number-group-addon"
        >
          http://
        </div>
        <div
          class="ant-input-number ant-input-number-filled ant-input-number-status-error"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="false"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              autocomplete="off"
              class="ant-input-number-input"
              placeholder="Filled"
              role="spinbutton"
              step="1"
              value=""
            />
          </div>
        </div>
        <div
          class="ant-input-number-group-addon"
        >
          .com
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id"
    style="gap: 12px;"
  >
    <div
      class="ant-input-number-group-wrapper ant-input-number-group-wrapper-filled css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-wrapper ant-input-number-group"
      >
        <div
          class="ant-input-number ant-input-number-filled"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="false"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              autocomplete="off"
              class="ant-input-number-input"
              placeholder="Filled"
              role="spinbutton"
              step="1"
              value=""
            />
          </div>
        </div>
        <div
          class="ant-input-number-group-addon"
        >
          .com
        </div>
      </div>
    </div>
    <div
      class="ant-input-number-group-wrapper ant-input-number-group-wrapper-disabled ant-input-number-group-wrapper-filled css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-wrapper ant-input-number-group"
      >
        <div
          class="ant-input-number ant-input-number-filled ant-input-number-disabled"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="false"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              autocomplete="off"
              class="ant-input-number-input"
              disabled=""
              placeholder="Filled"
              role="spinbutton"
              step="1"
              value=""
            />
          </div>
        </div>
        <div
          class="ant-input-number-group-addon"
        >
          .com
        </div>
      </div>
    </div>
    <div
      class="ant-input-number-group-wrapper ant-input-number-group-wrapper-filled ant-input-number-group-wrapper-status-error css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-wrapper ant-input-number-group"
      >
        <div
          class="ant-input-number ant-input-number-filled ant-input-number-status-error"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="false"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              autocomplete="off"
              class="ant-input-number-input"
              placeholder="Filled"
              role="spinbutton"
              step="1"
              value=""
            />
          </div>
        </div>
        <div
          class="ant-input-number-group-addon"
        >
          .com
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id"
    style="gap: 12px;"
  >
    <div
      class="ant-input-number-group-wrapper ant-input-number-group-wrapper-filled css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-wrapper ant-input-number-group"
      >
        <div
          class="ant-input-number-group-addon"
        >
          http://
        </div>
        <div
          class="ant-input-number ant-input-number-filled"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="false"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              autocomplete="off"
              class="ant-input-number-input"
              placeholder="Filled"
              role="spinbutton"
              step="1"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-input-number-group-wrapper ant-input-number-group-wrapper-disabled ant-input-number-group-wrapper-filled css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-wrapper ant-input-number-group"
      >
        <div
          class="ant-input-number-group-addon"
        >
          http://
        </div>
        <div
          class="ant-input-number ant-input-number-filled ant-input-number-disabled"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="false"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              autocomplete="off"
              class="ant-input-number-input"
              disabled=""
              placeholder="Filled"
              role="spinbutton"
              step="1"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-input-number-group-wrapper ant-input-number-group-wrapper-filled ant-input-number-group-wrapper-status-error css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-wrapper ant-input-number-group"
      >
        <div
          class="ant-input-number-group-addon"
        >
          http://
        </div>
        <div
          class="ant-input-number ant-input-number-filled ant-input-number-status-error"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="false"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              autocomplete="off"
              class="ant-input-number-input"
              placeholder="Filled"
              role="spinbutton"
              step="1"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/input-number/demo/filled-debug.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input-number/demo/focus.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  style="width: 100%;"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
      style="flex-wrap: wrap;"
    >
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
          type="button"
        >
          <span>
            Focus at first
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
          type="button"
        >
          <span>
            Focus at last
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
          type="button"
        >
          <span>
            Focus to select all
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
          type="button"
        >
          <span>
            Focus prevent scroll
          </span>
        </button>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number ant-input-number-outlined css-var-test-id ant-input-number-css-var"
      style="width: 100%;"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="false"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          aria-valuenow="999"
          autocomplete="off"
          class="ant-input-number-input"
          role="spinbutton"
          step="1"
          value="999"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/input-number/demo/focus.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input-number/demo/formatter.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number ant-input-number-outlined css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="false"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          aria-valuenow="1000"
          autocomplete="off"
          class="ant-input-number-input"
          role="spinbutton"
          step="1"
          value="$ 1,000"
        />
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number ant-input-number-outlined css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="true"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up ant-input-number-handler-up-disabled"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          aria-valuemax="100"
          aria-valuemin="0"
          aria-valuenow="100"
          autocomplete="off"
          class="ant-input-number-input"
          role="spinbutton"
          step="1"
          value="100%"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/input-number/demo/formatter.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input-number/demo/keyboard.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number ant-input-number-outlined css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="false"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          aria-valuemax="10"
          aria-valuemin="1"
          aria-valuenow="3"
          autocomplete="off"
          class="ant-input-number-input"
          role="spinbutton"
          step="1"
          value="3"
        />
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <label
      class="ant-checkbox-wrapper ant-checkbox-wrapper-checked css-var-test-id ant-checkbox-css-var"
    >
      <span
        class="ant-checkbox ant-wave-target ant-checkbox-checked"
      >
        <input
          checked=""
          class="ant-checkbox-input"
          type="checkbox"
        />
        <span
          class="ant-checkbox-inner"
        />
      </span>
      <span
        class="ant-checkbox-label"
      >
        Toggle keyboard
      </span>
    </label>
  </div>
</div>
`;

exports[`renders components/input-number/demo/keyboard.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input-number/demo/out-of-range.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number ant-input-number-outlined css-var-test-id ant-input-number-css-var ant-input-number-out-of-range"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="true"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up ant-input-number-handler-up-disabled"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          aria-valuemax="10"
          aria-valuemin="1"
          aria-valuenow="99"
          autocomplete="off"
          class="ant-input-number-input"
          role="spinbutton"
          step="1"
          value="99"
        />
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Reset
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/input-number/demo/out-of-range.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input-number/demo/presuffix.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-input-number-affix-wrapper ant-input-number-outlined css-var-test-id ant-input-number-css-var"
    style="width: 100%;"
  >
    <span
      class="ant-input-number-prefix"
    >
      ￥
    </span>
    <div
      class="ant-input-number"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="false"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          autocomplete="off"
          class="ant-input-number-input"
          role="spinbutton"
          step="1"
          value=""
        />
      </div>
    </div>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-input-number-group-wrapper ant-input-number-group-wrapper-outlined css-var-test-id ant-input-number-css-var"
    style="width: 100%;"
  >
    <div
      class="ant-input-number-wrapper ant-input-number-group"
    >
      <div
        class="ant-input-number-group-addon"
      >
        <span
          aria-label="user"
          class="anticon anticon-user"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="user"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
            />
          </svg>
        </span>
      </div>
      <div
        class="ant-input-number-affix-wrapper ant-input-number-outlined"
      >
        <span
          class="ant-input-number-prefix"
        >
          ￥
        </span>
        <div
          class="ant-input-number"
        >
          <div
            class="ant-input-number-handler-wrap"
          >
            <span
              aria-disabled="false"
              aria-label="Increase Value"
              class="ant-input-number-handler ant-input-number-handler-up"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="up"
                class="anticon anticon-up ant-input-number-handler-up-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="up"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                  />
                </svg>
              </span>
            </span>
            <span
              aria-disabled="false"
              aria-label="Decrease Value"
              class="ant-input-number-handler ant-input-number-handler-down"
              role="button"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-input-number-handler-down-inner"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
          <div
            class="ant-input-number-input-wrap"
          >
            <input
              autocomplete="off"
              class="ant-input-number-input"
              role="spinbutton"
              step="1"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-input-number-affix-wrapper ant-input-number-disabled ant-input-number-affix-wrapper-disabled ant-input-number-affix-wrapper-without-controls ant-input-number-outlined css-var-test-id ant-input-number-css-var"
    style="width: 100%;"
  >
    <span
      class="ant-input-number-prefix"
    >
      ￥
    </span>
    <div
      class="ant-input-number ant-input-number-disabled"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="false"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          autocomplete="off"
          class="ant-input-number-input"
          disabled=""
          role="spinbutton"
          step="1"
          value=""
        />
      </div>
    </div>
  </div>,
  <br />,
  <br />,
  <div
    class="ant-input-number-affix-wrapper ant-input-number-outlined css-var-test-id ant-input-number-css-var"
    style="width: 100%;"
  >
    <div
      class="ant-input-number"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="false"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          autocomplete="off"
          class="ant-input-number-input"
          role="spinbutton"
          step="1"
          value=""
        />
      </div>
    </div>
    <span
      class="ant-input-number-suffix"
    >
      RMB
    </span>
  </div>,
]
`;

exports[`renders components/input-number/demo/presuffix.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input-number/demo/render-panel.tsx extend context correctly 1`] = `
<div
  style="display: flex; flex-direction: column; row-gap: 16px;"
>
  <div
    class="ant-input-number ant-input-number-outlined css-var-test-id ant-input-number-css-var"
    style="width: 100%;"
  >
    <div
      class="ant-input-number-handler-wrap"
    >
      <span
        aria-disabled="false"
        aria-label="Increase Value"
        class="ant-input-number-handler ant-input-number-handler-up"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="up"
          class="anticon anticon-up ant-input-number-handler-up-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="up"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
            />
          </svg>
        </span>
      </span>
      <span
        aria-disabled="false"
        aria-label="Decrease Value"
        class="ant-input-number-handler ant-input-number-handler-down"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-input-number-handler-down-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-input-number-input-wrap"
    >
      <input
        autocomplete="off"
        class="ant-input-number-input"
        role="spinbutton"
        step="1"
        value=""
      />
    </div>
  </div>
</div>
`;

exports[`renders components/input-number/demo/render-panel.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input-number/demo/size.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  style="flex-wrap: wrap;"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number ant-input-number-lg ant-input-number-outlined css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="false"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          aria-valuemax="100000"
          aria-valuemin="1"
          aria-valuenow="3"
          autocomplete="off"
          class="ant-input-number-input"
          role="spinbutton"
          step="1"
          value="3"
        />
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number ant-input-number-outlined css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="false"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          aria-valuemax="100000"
          aria-valuemin="1"
          aria-valuenow="3"
          autocomplete="off"
          class="ant-input-number-input"
          role="spinbutton"
          step="1"
          value="3"
        />
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number ant-input-number-sm ant-input-number-outlined css-var-test-id ant-input-number-css-var"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="false"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          aria-valuemax="100000"
          aria-valuemin="1"
          aria-valuenow="3"
          autocomplete="off"
          class="ant-input-number-input"
          role="spinbutton"
          step="1"
          value="3"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/input-number/demo/size.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input-number/demo/status.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  style="width: 100%;"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number ant-input-number-outlined ant-input-number-status-error css-var-test-id ant-input-number-css-var"
      style="width: 100%;"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="false"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          autocomplete="off"
          class="ant-input-number-input"
          role="spinbutton"
          step="1"
          value=""
        />
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number ant-input-number-outlined ant-input-number-status-warning css-var-test-id ant-input-number-css-var"
      style="width: 100%;"
    >
      <div
        class="ant-input-number-handler-wrap"
      >
        <span
          aria-disabled="false"
          aria-label="Increase Value"
          class="ant-input-number-handler ant-input-number-handler-up"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="up"
            class="anticon anticon-up ant-input-number-handler-up-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="up"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
              />
            </svg>
          </span>
        </span>
        <span
          aria-disabled="false"
          aria-label="Decrease Value"
          class="ant-input-number-handler ant-input-number-handler-down"
          role="button"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-input-number-handler-down-inner"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-input-number-input-wrap"
      >
        <input
          autocomplete="off"
          class="ant-input-number-input"
          role="spinbutton"
          step="1"
          value=""
        />
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number-affix-wrapper ant-input-number-outlined ant-input-number-status-error css-var-test-id ant-input-number-css-var"
      style="width: 100%;"
    >
      <span
        class="ant-input-number-prefix"
      >
        <span
          aria-label="clock-circle"
          class="anticon anticon-clock-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="clock-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
            />
          </svg>
        </span>
      </span>
      <div
        class="ant-input-number"
      >
        <div
          class="ant-input-number-handler-wrap"
        >
          <span
            aria-disabled="false"
            aria-label="Increase Value"
            class="ant-input-number-handler ant-input-number-handler-up"
            role="button"
            unselectable="on"
          >
            <span
              aria-label="up"
              class="anticon anticon-up ant-input-number-handler-up-inner"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="up"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                />
              </svg>
            </span>
          </span>
          <span
            aria-disabled="false"
            aria-label="Decrease Value"
            class="ant-input-number-handler ant-input-number-handler-down"
            role="button"
            unselectable="on"
          >
            <span
              aria-label="down"
              class="anticon anticon-down ant-input-number-handler-down-inner"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="down"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                />
              </svg>
            </span>
          </span>
        </div>
        <div
          class="ant-input-number-input-wrap"
        >
          <input
            autocomplete="off"
            class="ant-input-number-input"
            role="spinbutton"
            step="1"
            value=""
          />
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-input-number-affix-wrapper ant-input-number-outlined ant-input-number-status-warning css-var-test-id ant-input-number-css-var"
      style="width: 100%;"
    >
      <span
        class="ant-input-number-prefix"
      >
        <span
          aria-label="clock-circle"
          class="anticon anticon-clock-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="clock-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
            />
          </svg>
        </span>
      </span>
      <div
        class="ant-input-number"
      >
        <div
          class="ant-input-number-handler-wrap"
        >
          <span
            aria-disabled="false"
            aria-label="Increase Value"
            class="ant-input-number-handler ant-input-number-handler-up"
            role="button"
            unselectable="on"
          >
            <span
              aria-label="up"
              class="anticon anticon-up ant-input-number-handler-up-inner"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="up"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                />
              </svg>
            </span>
          </span>
          <span
            aria-disabled="false"
            aria-label="Decrease Value"
            class="ant-input-number-handler ant-input-number-handler-down"
            role="button"
            unselectable="on"
          >
            <span
              aria-label="down"
              class="anticon anticon-down ant-input-number-handler-down-inner"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="down"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                />
              </svg>
            </span>
          </span>
        </div>
        <div
          class="ant-input-number-input-wrap"
        >
          <input
            autocomplete="off"
            class="ant-input-number-input"
            role="spinbutton"
            step="1"
            value=""
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/input-number/demo/status.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input-number/demo/variant.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-vertical"
  style="gap: 12px;"
>
  <div
    class="ant-input-number ant-input-number-outlined css-var-test-id ant-input-number-css-var"
    style="width: 200px;"
  >
    <div
      class="ant-input-number-handler-wrap"
    >
      <span
        aria-disabled="false"
        aria-label="Increase Value"
        class="ant-input-number-handler ant-input-number-handler-up"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="up"
          class="anticon anticon-up ant-input-number-handler-up-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="up"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
            />
          </svg>
        </span>
      </span>
      <span
        aria-disabled="false"
        aria-label="Decrease Value"
        class="ant-input-number-handler ant-input-number-handler-down"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-input-number-handler-down-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-input-number-input-wrap"
    >
      <input
        autocomplete="off"
        class="ant-input-number-input"
        placeholder="Outlined"
        role="spinbutton"
        step="1"
        value=""
      />
    </div>
  </div>
  <div
    class="ant-input-number ant-input-number-filled css-var-test-id ant-input-number-css-var"
    style="width: 200px;"
  >
    <div
      class="ant-input-number-handler-wrap"
    >
      <span
        aria-disabled="false"
        aria-label="Increase Value"
        class="ant-input-number-handler ant-input-number-handler-up"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="up"
          class="anticon anticon-up ant-input-number-handler-up-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="up"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
            />
          </svg>
        </span>
      </span>
      <span
        aria-disabled="false"
        aria-label="Decrease Value"
        class="ant-input-number-handler ant-input-number-handler-down"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-input-number-handler-down-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-input-number-input-wrap"
    >
      <input
        autocomplete="off"
        class="ant-input-number-input"
        placeholder="Filled"
        role="spinbutton"
        step="1"
        value=""
      />
    </div>
  </div>
  <div
    class="ant-input-number ant-input-number-borderless css-var-test-id ant-input-number-css-var"
    style="width: 200px;"
  >
    <div
      class="ant-input-number-handler-wrap"
    >
      <span
        aria-disabled="false"
        aria-label="Increase Value"
        class="ant-input-number-handler ant-input-number-handler-up"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="up"
          class="anticon anticon-up ant-input-number-handler-up-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="up"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
            />
          </svg>
        </span>
      </span>
      <span
        aria-disabled="false"
        aria-label="Decrease Value"
        class="ant-input-number-handler ant-input-number-handler-down"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-input-number-handler-down-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-input-number-input-wrap"
    >
      <input
        autocomplete="off"
        class="ant-input-number-input"
        placeholder="Borderless"
        role="spinbutton"
        step="1"
        value=""
      />
    </div>
  </div>
  <div
    class="ant-input-number ant-input-number-underlined css-var-test-id ant-input-number-css-var"
    style="width: 200px;"
  >
    <div
      class="ant-input-number-handler-wrap"
    >
      <span
        aria-disabled="false"
        aria-label="Increase Value"
        class="ant-input-number-handler ant-input-number-handler-up"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="up"
          class="anticon anticon-up ant-input-number-handler-up-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="up"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
            />
          </svg>
        </span>
      </span>
      <span
        aria-disabled="false"
        aria-label="Decrease Value"
        class="ant-input-number-handler ant-input-number-handler-down"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-input-number-handler-down-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-input-number-input-wrap"
    >
      <input
        autocomplete="off"
        class="ant-input-number-input"
        placeholder="Underlined"
        role="spinbutton"
        step="1"
        value=""
      />
    </div>
  </div>
</div>
`;

exports[`renders components/input-number/demo/variant.tsx extend context correctly 2`] = `[]`;
