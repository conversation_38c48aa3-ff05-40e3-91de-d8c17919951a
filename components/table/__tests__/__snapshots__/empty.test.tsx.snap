// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Table renders empty table 1`] = `
<div
  class="css-var-root ant-table-css-var ant-table-wrapper"
>
  <div
    class="ant-spin-nested-loading css-var-root"
  >
    <div
      class="ant-spin-container"
    >
      <div
        class="ant-table ant-table-empty css-var-root ant-table-css-var"
      >
        <div
          class="ant-table-container"
        >
          <div
            class="ant-table-content"
          >
            <table
              style="table-layout: auto;"
            >
              <thead
                class="ant-table-thead"
              >
                <tr>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 1
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 2
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 3
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 4
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 5
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 6
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 7
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 8
                  </th>
                </tr>
              </thead>
              <tbody
                class="ant-table-tbody"
              >
                <tr
                  class="ant-table-placeholder"
                >
                  <td
                    class="ant-table-cell"
                    colspan="8"
                  >
                    <div
                      class="css-var-root ant-empty ant-empty-normal"
                    >
                      <div
                        class="ant-empty-image"
                      >
                        <svg
                          height="41"
                          viewBox="0 0 64 41"
                          width="64"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <title>
                            No data
                          </title>
                          <g
                            fill="none"
                            fill-rule="evenodd"
                            transform="translate(0 1)"
                          >
                            <ellipse
                              cx="32"
                              cy="33"
                              fill="#f5f5f5"
                              rx="32"
                              ry="7"
                            />
                            <g
                              fill-rule="nonzero"
                              stroke="#d9d9d9"
                            >
                              <path
                                d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                              />
                              <path
                                d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                                fill="#fafafa"
                              />
                            </g>
                          </g>
                        </svg>
                      </div>
                      <div
                        class="ant-empty-description"
                      >
                        No data
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Table renders empty table when emptyText is null 1`] = `
<div
  class="css-var-root ant-table-css-var ant-table-wrapper"
>
  <div
    class="ant-spin-nested-loading css-var-root"
  >
    <div
      class="ant-spin-container"
    >
      <div
        class="ant-table ant-table-empty css-var-root ant-table-css-var"
      >
        <div
          class="ant-table-container"
        >
          <div
            class="ant-table-content"
          >
            <table
              style="table-layout: auto;"
            >
              <thead
                class="ant-table-thead"
              >
                <tr>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 1
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 2
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 3
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 4
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 5
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 6
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 7
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 8
                  </th>
                </tr>
              </thead>
              <tbody
                class="ant-table-tbody"
              >
                <tr
                  class="ant-table-placeholder"
                >
                  <td
                    class="ant-table-cell"
                    colspan="8"
                  />
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Table renders empty table with custom emptyText 1`] = `
<div
  class="css-var-root ant-table-css-var ant-table-wrapper"
>
  <div
    class="ant-spin-nested-loading css-var-root"
  >
    <div
      class="ant-spin-container"
    >
      <div
        class="ant-table ant-table-empty css-var-root ant-table-css-var"
      >
        <div
          class="ant-table-container"
        >
          <div
            class="ant-table-content"
          >
            <table
              style="table-layout: auto;"
            >
              <thead
                class="ant-table-thead"
              >
                <tr>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 1
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 2
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 3
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 4
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 5
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 6
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 7
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 8
                  </th>
                </tr>
              </thead>
              <tbody
                class="ant-table-tbody"
              >
                <tr
                  class="ant-table-placeholder"
                >
                  <td
                    class="ant-table-cell"
                    colspan="8"
                  >
                    custom empty text
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Table renders empty table with fixed columns should work 1`] = `
<div
  class="css-var-root ant-table-css-var ant-table-wrapper"
>
  <div
    class="ant-spin-nested-loading css-var-root"
  >
    <div
      class="ant-spin-container"
    >
      <div
        class="ant-table ant-table-empty css-var-root ant-table-css-var ant-table-fix-start-shadow ant-table-fix-end-shadow ant-table-fixed-column ant-table-scroll-horizontal ant-table-has-fix-start ant-table-has-fix-end"
      >
        <div
          class="ant-table-container"
        >
          <div
            class="ant-table-content"
            style="overflow-x: auto; overflow-y: hidden;"
          >
            <table
              style="width: 1px; min-width: 100%; table-layout: fixed;"
            >
              <colgroup>
                <col
                  style="width: 100px;"
                />
                <col
                  style="width: 100px;"
                />
                <col />
                <col />
                <col />
                <col />
                <col />
                <col />
                <col />
                <col />
                <col
                  style="width: 100px;"
                />
              </colgroup>
              <thead
                class="ant-table-thead"
              >
                <tr>
                  <th
                    class="ant-table-cell ant-table-cell-fix ant-table-cell-fix-start"
                    scope="col"
                    style="inset-inline-start: 0; --z-offset: 22; --z-offset-reverse: 11;"
                  >
                    Full Name
                  </th>
                  <th
                    class="ant-table-cell ant-table-cell-fix ant-table-cell-fix-start ant-table-cell-fix-start-shadow"
                    scope="col"
                    style="inset-inline-start: 0; --z-offset: 21; --z-offset-reverse: 12;"
                  >
                    Age
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 1
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 2
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 3
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 4
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 5
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 6
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 7
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 8
                  </th>
                  <th
                    class="ant-table-cell ant-table-cell-fix ant-table-cell-fix-end ant-table-cell-fix-end-shadow"
                    scope="col"
                    style="inset-inline-end: 0; --z-offset: 10; --z-offset-reverse: 1;"
                  >
                    Action
                  </th>
                </tr>
              </thead>
              <tbody
                class="ant-table-tbody"
              >
                <tr
                  aria-hidden="true"
                  class="ant-table-measure-row"
                  style="height: 0px;"
                >
                  <td
                    style="padding-top: 0px; padding-bottom: 0px; border-top: 0px; border-bottom: 0px; height: 0px;"
                  >
                    <div
                      style="height: 0px; overflow: hidden; font-weight: bold;"
                    >
                      Full Name
                    </div>
                  </td>
                  <td
                    style="padding-top: 0px; padding-bottom: 0px; border-top: 0px; border-bottom: 0px; height: 0px;"
                  >
                    <div
                      style="height: 0px; overflow: hidden; font-weight: bold;"
                    >
                      Age
                    </div>
                  </td>
                  <td
                    style="padding-top: 0px; padding-bottom: 0px; border-top: 0px; border-bottom: 0px; height: 0px;"
                  >
                    <div
                      style="height: 0px; overflow: hidden; font-weight: bold;"
                    >
                      Column 1
                    </div>
                  </td>
                  <td
                    style="padding-top: 0px; padding-bottom: 0px; border-top: 0px; border-bottom: 0px; height: 0px;"
                  >
                    <div
                      style="height: 0px; overflow: hidden; font-weight: bold;"
                    >
                      Column 2
                    </div>
                  </td>
                  <td
                    style="padding-top: 0px; padding-bottom: 0px; border-top: 0px; border-bottom: 0px; height: 0px;"
                  >
                    <div
                      style="height: 0px; overflow: hidden; font-weight: bold;"
                    >
                      Column 3
                    </div>
                  </td>
                  <td
                    style="padding-top: 0px; padding-bottom: 0px; border-top: 0px; border-bottom: 0px; height: 0px;"
                  >
                    <div
                      style="height: 0px; overflow: hidden; font-weight: bold;"
                    >
                      Column 4
                    </div>
                  </td>
                  <td
                    style="padding-top: 0px; padding-bottom: 0px; border-top: 0px; border-bottom: 0px; height: 0px;"
                  >
                    <div
                      style="height: 0px; overflow: hidden; font-weight: bold;"
                    >
                      Column 5
                    </div>
                  </td>
                  <td
                    style="padding-top: 0px; padding-bottom: 0px; border-top: 0px; border-bottom: 0px; height: 0px;"
                  >
                    <div
                      style="height: 0px; overflow: hidden; font-weight: bold;"
                    >
                      Column 6
                    </div>
                  </td>
                  <td
                    style="padding-top: 0px; padding-bottom: 0px; border-top: 0px; border-bottom: 0px; height: 0px;"
                  >
                    <div
                      style="height: 0px; overflow: hidden; font-weight: bold;"
                    >
                      Column 7
                    </div>
                  </td>
                  <td
                    style="padding-top: 0px; padding-bottom: 0px; border-top: 0px; border-bottom: 0px; height: 0px;"
                  >
                    <div
                      style="height: 0px; overflow: hidden; font-weight: bold;"
                    >
                      Column 8
                    </div>
                  </td>
                  <td
                    style="padding-top: 0px; padding-bottom: 0px; border-top: 0px; border-bottom: 0px; height: 0px;"
                  >
                    <div
                      style="height: 0px; overflow: hidden; font-weight: bold;"
                    >
                      Action
                    </div>
                  </td>
                </tr>
                <tr
                  class="ant-table-placeholder"
                >
                  <td
                    class="ant-table-cell"
                    colspan="11"
                  >
                    <div
                      class="ant-table-expanded-row-fixed"
                      style="width: 1000px; position: sticky; left: 0px; overflow: hidden;"
                    >
                      <div
                        class="css-var-root ant-empty ant-empty-normal"
                      >
                        <div
                          class="ant-empty-image"
                        >
                          <svg
                            height="41"
                            viewBox="0 0 64 41"
                            width="64"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <title>
                              No data
                            </title>
                            <g
                              fill="none"
                              fill-rule="evenodd"
                              transform="translate(0 1)"
                            >
                              <ellipse
                                cx="32"
                                cy="33"
                                fill="#f5f5f5"
                                rx="32"
                                ry="7"
                              />
                              <g
                                fill-rule="nonzero"
                                stroke="#d9d9d9"
                              >
                                <path
                                  d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                                />
                                <path
                                  d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                                  fill="#fafafa"
                                />
                              </g>
                            </g>
                          </svg>
                        </div>
                        <div
                          class="ant-empty-description"
                        >
                          No data
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Table renders empty table without emptyText when loading 1`] = `
<div
  class="css-var-root ant-table-css-var ant-table-wrapper"
>
  <div
    class="ant-spin-nested-loading css-var-root"
  >
    <div>
      <div
        aria-busy="true"
        aria-live="polite"
        class="ant-spin ant-spin-spinning css-var-root"
      >
        <span
          class="ant-spin-dot-holder"
        >
          <span
            class="ant-spin-dot ant-spin-dot-spin"
          >
            <i
              class="ant-spin-dot-item"
            />
            <i
              class="ant-spin-dot-item"
            />
            <i
              class="ant-spin-dot-item"
            />
            <i
              class="ant-spin-dot-item"
            />
          </span>
        </span>
      </div>
    </div>
    <div
      class="ant-spin-container ant-spin-blur"
    >
      <div
        class="ant-table ant-table-empty css-var-root ant-table-css-var"
      >
        <div
          class="ant-table-container"
        >
          <div
            class="ant-table-content"
          >
            <table
              style="table-layout: auto;"
            >
              <thead
                class="ant-table-thead"
              >
                <tr>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 1
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 2
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 3
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 4
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 5
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 6
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 7
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 8
                  </th>
                </tr>
              </thead>
              <tbody
                class="ant-table-tbody"
              >
                <tr
                  class="ant-table-placeholder"
                >
                  <td
                    class="ant-table-cell"
                    colspan="8"
                  >
                    <div
                      class="css-var-root ant-empty ant-empty-normal"
                    >
                      <div
                        class="ant-empty-image"
                      >
                        <svg
                          height="41"
                          viewBox="0 0 64 41"
                          width="64"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <title>
                            No data
                          </title>
                          <g
                            fill="none"
                            fill-rule="evenodd"
                            transform="translate(0 1)"
                          >
                            <ellipse
                              cx="32"
                              cy="33"
                              fill="#f5f5f5"
                              rx="32"
                              ry="7"
                            />
                            <g
                              fill-rule="nonzero"
                              stroke="#d9d9d9"
                            >
                              <path
                                d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                              />
                              <path
                                d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                                fill="#fafafa"
                              />
                            </g>
                          </g>
                        </svg>
                      </div>
                      <div
                        class="ant-empty-description"
                      >
                        No data
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Table should not render empty when loading 1`] = `
<div
  class="css-var-root ant-table-css-var ant-table-wrapper"
>
  <div
    class="ant-spin-nested-loading css-var-root"
  >
    <div>
      <div
        aria-busy="true"
        aria-live="polite"
        class="ant-spin ant-spin-spinning css-var-root"
      >
        <span
          class="ant-spin-dot-holder"
        >
          <span
            class="ant-spin-dot ant-spin-dot-spin"
          >
            <i
              class="ant-spin-dot-item"
            />
            <i
              class="ant-spin-dot-item"
            />
            <i
              class="ant-spin-dot-item"
            />
            <i
              class="ant-spin-dot-item"
            />
          </span>
        </span>
      </div>
    </div>
    <div
      class="ant-spin-container ant-spin-blur"
    >
      <div
        class="ant-table ant-table-empty css-var-root ant-table-css-var"
      >
        <div
          class="ant-table-container"
        >
          <div
            class="ant-table-content"
          >
            <table
              style="table-layout: auto;"
            >
              <thead
                class="ant-table-thead"
              >
                <tr>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 1
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 2
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 3
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 4
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 5
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 6
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 7
                  </th>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Column 8
                  </th>
                </tr>
              </thead>
              <tbody
                class="ant-table-tbody"
              >
                <tr
                  class="ant-table-placeholder"
                >
                  <td
                    class="ant-table-cell"
                    colspan="8"
                  />
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
