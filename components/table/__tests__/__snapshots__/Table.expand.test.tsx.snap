// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Table.expand click to expand 1`] = `
<div
  class="css-var-root ant-table-css-var ant-table-wrapper"
>
  <div
    class="ant-spin-nested-loading css-var-root"
  >
    <div
      class="ant-spin-container"
    >
      <div
        class="ant-table css-var-root ant-table-css-var"
      >
        <div
          class="ant-table-container"
        >
          <div
            class="ant-table-content"
          >
            <table
              style="table-layout: auto;"
            >
              <thead
                class="ant-table-thead"
              >
                <tr>
                  <th
                    class="ant-table-cell"
                    scope="col"
                  >
                    Name
                  </th>
                </tr>
              </thead>
              <tbody
                class="ant-table-tbody"
              >
                <tr
                  class="ant-table-row ant-table-row-level-0"
                  data-row-key="1"
                >
                  <td
                    class="ant-table-cell ant-table-cell-with-append"
                  >
                    <span
                      class="ant-table-row-indent indent-level-0"
                      style="padding-left: 0px;"
                    />
                    <button
                      aria-expanded="true"
                      aria-label="Collapse row"
                      class="ant-table-row-expand-icon ant-table-row-expand-icon-expanded"
                      type="button"
                    />
                  </td>
                </tr>
                <tr
                  class="ant-table-row ant-table-row-level-1"
                  data-row-key="2"
                >
                  <td
                    class="ant-table-cell ant-table-cell-with-append"
                  >
                    <span
                      class="ant-table-row-indent indent-level-1"
                      style="padding-left: 15px;"
                    />
                    <button
                      aria-expanded="false"
                      aria-label="Expand row"
                      class="ant-table-row-expand-icon ant-table-row-expand-icon-spaced"
                      type="button"
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <ul
        class="ant-pagination ant-table-pagination ant-table-pagination-end css-var-root"
      >
        <li
          aria-disabled="true"
          class="ant-pagination-prev ant-pagination-disabled"
          title="Previous Page"
        >
          <button
            class="ant-pagination-item-link"
            disabled=""
            tabindex="-1"
            type="button"
          >
            <span
              aria-label="left"
              class="anticon anticon-left"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="left"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"
                />
              </svg>
            </span>
          </button>
        </li>
        <li
          class="ant-pagination-item ant-pagination-item-1 ant-pagination-item-active"
          tabindex="0"
          title="1"
        >
          <a
            rel="nofollow"
          >
            1
          </a>
        </li>
        <li
          aria-disabled="true"
          class="ant-pagination-next ant-pagination-disabled"
          title="Next Page"
        >
          <button
            class="ant-pagination-item-link"
            disabled=""
            tabindex="-1"
            type="button"
          >
            <span
              aria-label="right"
              class="anticon anticon-right"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
          </button>
        </li>
      </ul>
    </div>
  </div>
</div>
`;
