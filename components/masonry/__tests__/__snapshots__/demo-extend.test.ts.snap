// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/masonry/demo/basic.tsx extend context correctly 1`] = `
<div
  class="ant-masonry css-var-test-id"
  style="height: 0px;"
>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 150px;"
    >
      <div
        class="ant-card-body"
      >
        1
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 50px;"
    >
      <div
        class="ant-card-body"
      >
        2
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 90px;"
    >
      <div
        class="ant-card-body"
      >
        3
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 70px;"
    >
      <div
        class="ant-card-body"
      >
        4
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
    >
      <div
        class="ant-card-cover"
      >
        <img
          alt="food"
          src="https://images.unsplash.com/photo-1491961865842-98f7befd1a60?w=523&auto=format"
        />
      </div>
      <div
        class="ant-card-body"
      >
        <div
          class="ant-card-meta"
        >
          <div
            class="ant-card-meta-section"
          >
            <div
              class="ant-card-meta-title"
            >
              I'm Special
            </div>
            <div
              class="ant-card-meta-description"
            >
              Let's have a meal
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 150px;"
    >
      <div
        class="ant-card-body"
      >
        6
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 130px;"
    >
      <div
        class="ant-card-body"
      >
        7
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 80px;"
    >
      <div
        class="ant-card-body"
      >
        8
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 50px;"
    >
      <div
        class="ant-card-body"
      >
        9
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 90px;"
    >
      <div
        class="ant-card-body"
      >
        10
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 100px;"
    >
      <div
        class="ant-card-body"
      >
        11
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 150px;"
    >
      <div
        class="ant-card-body"
      >
        12
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 60px;"
    >
      <div
        class="ant-card-body"
      >
        13
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 50px;"
    >
      <div
        class="ant-card-body"
      >
        14
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 80px;"
    >
      <div
        class="ant-card-body"
      >
        15
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/masonry/demo/basic.tsx extend context correctly 2`] = `[]`;

exports[`renders components/masonry/demo/fresh.tsx extend context correctly 1`] = `
<div
  class="ant-masonry css-var-test-id"
  style="height: 0px;"
>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 150px; transition: height 0.3s;"
    >
      <div
        class="ant-card-body"
      >
        1 - Click
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 50px; transition: height 0.3s;"
    >
      <div
        class="ant-card-body"
      >
        2 - Click
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 90px; transition: height 0.3s;"
    >
      <div
        class="ant-card-body"
      >
        3 - Click
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 70px; transition: height 0.3s;"
    >
      <div
        class="ant-card-body"
      >
        4 - Click
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 110px; transition: height 0.3s;"
    >
      <div
        class="ant-card-body"
      >
        5 - Click
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 150px; transition: height 0.3s;"
    >
      <div
        class="ant-card-body"
      >
        6 - Click
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 130px; transition: height 0.3s;"
    >
      <div
        class="ant-card-body"
      >
        7 - Click
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 80px; transition: height 0.3s;"
    >
      <div
        class="ant-card-body"
      >
        8 - Click
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 50px; transition: height 0.3s;"
    >
      <div
        class="ant-card-body"
      >
        9 - Click
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 90px; transition: height 0.3s;"
    >
      <div
        class="ant-card-body"
      >
        10 - Click
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 100px; transition: height 0.3s;"
    >
      <div
        class="ant-card-body"
      >
        11 - Click
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 150px; transition: height 0.3s;"
    >
      <div
        class="ant-card-body"
      >
        12 - Click
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 60px; transition: height 0.3s;"
    >
      <div
        class="ant-card-body"
      >
        13 - Click
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 50px; transition: height 0.3s;"
    >
      <div
        class="ant-card-body"
      >
        14 - Click
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 80px; transition: height 0.3s;"
    >
      <div
        class="ant-card-body"
      >
        15 - Click
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/masonry/demo/fresh.tsx extend context correctly 2`] = `[]`;

exports[`renders components/masonry/demo/image.tsx extend context correctly 1`] = `
<div
  class="ant-masonry css-var-test-id"
  style="height: 0px;"
>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <img
      alt="sample"
      src="https://images.unsplash.com/photo-1510001618818-4b4e3d86bf0f?w=523&auto=format"
      style="width: 100%;"
    />
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <img
      alt="sample"
      src="https://images.unsplash.com/photo-1507513319174-e556268bb244?w=523&auto=format"
      style="width: 100%;"
    />
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <img
      alt="sample"
      src="https://images.unsplash.com/photo-1474181487882-5abf3f0ba6c2?w=523&auto=format"
      style="width: 100%;"
    />
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <img
      alt="sample"
      src="https://images.unsplash.com/photo-1492778297155-7be4c83960c7?w=523&auto=format"
      style="width: 100%;"
    />
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <img
      alt="sample"
      src="https://images.unsplash.com/photo-1508062878650-88b52897f298?w=523&auto=format"
      style="width: 100%;"
    />
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <img
      alt="sample"
      src="https://images.unsplash.com/photo-1506158278516-d720e72406fc?w=523&auto=format"
      style="width: 100%;"
    />
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <img
      alt="sample"
      src="https://images.unsplash.com/photo-1552203274-e3c7bd771d26?w=523&auto=format"
      style="width: 100%;"
    />
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <img
      alt="sample"
      src="https://images.unsplash.com/photo-1528163186890-de9b86b54b51?w=523&auto=format"
      style="width: 100%;"
    />
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <img
      alt="sample"
      src="https://images.unsplash.com/photo-1727423304224-6d2fd99b864c?w=523&auto=format"
      style="width: 100%;"
    />
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <img
      alt="sample"
      src="https://images.unsplash.com/photo-1675090391405-432434e23595?w=523&auto=format"
      style="width: 100%;"
    />
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <img
      alt="sample"
      src="https://images.unsplash.com/photo-1554196967-97a8602084d9?w=523&auto=format"
      style="width: 100%;"
    />
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <img
      alt="sample"
      src="https://images.unsplash.com/photo-1491961865842-98f7befd1a60?w=523&auto=format"
      style="width: 100%;"
    />
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <img
      alt="sample"
      src="https://images.unsplash.com/photo-1721728613411-d56d2ddda959?w=523&auto=format"
      style="width: 100%;"
    />
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <img
      alt="sample"
      src="https://images.unsplash.com/photo-1731901245099-20ac7f85dbaa?w=523&auto=format"
      style="width: 100%;"
    />
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <img
      alt="sample"
      src="https://images.unsplash.com/photo-1617694455303-59af55af7e58?w=523&auto=format"
      style="width: 100%;"
    />
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
  >
    <img
      alt="sample"
      src="https://images.unsplash.com/photo-1709198165282-1dab551df890?w=523&auto=format"
      style="width: 100%;"
    />
  </div>
</div>
`;

exports[`renders components/masonry/demo/image.tsx extend context correctly 2`] = `[]`;

exports[`renders components/masonry/demo/responsive.tsx extend context correctly 1`] = `
<div
  class="ant-masonry css-var-test-id"
  style="height: 0px;"
>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 8px) / 1); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 8px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 120px;"
    >
      <div
        class="ant-card-body"
      >
        1
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 8px) / 1); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 8px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 55px;"
    >
      <div
        class="ant-card-body"
      >
        2
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 8px) / 1); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 8px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 85px;"
    >
      <div
        class="ant-card-body"
      >
        3
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 8px) / 1); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 8px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 160px;"
    >
      <div
        class="ant-card-body"
      >
        4
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 8px) / 1); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 8px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 95px;"
    >
      <div
        class="ant-card-body"
      >
        5
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 8px) / 1); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 8px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 140px;"
    >
      <div
        class="ant-card-body"
      >
        6
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 8px) / 1); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 8px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 75px;"
    >
      <div
        class="ant-card-body"
      >
        7
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 8px) / 1); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 8px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 110px;"
    >
      <div
        class="ant-card-body"
      >
        8
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 8px) / 1); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 8px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 65px;"
    >
      <div
        class="ant-card-body"
      >
        9
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 8px) / 1); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 8px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 130px;"
    >
      <div
        class="ant-card-body"
      >
        10
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 8px) / 1); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 8px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 90px;"
    >
      <div
        class="ant-card-body"
      >
        11
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 8px) / 1); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 8px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 145px;"
    >
      <div
        class="ant-card-body"
      >
        12
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 8px) / 1); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 8px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 55px;"
    >
      <div
        class="ant-card-body"
      >
        13
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 8px) / 1); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 8px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 100px;"
    >
      <div
        class="ant-card-body"
      >
        14
      </div>
    </div>
  </div>
  <div
    class="ant-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
    style="--item-width: calc((100% + 8px) / 1); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 8px); position: absolute;"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small css-var-test-id"
      style="height: 80px;"
    >
      <div
        class="ant-card-body"
      >
        15
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/masonry/demo/responsive.tsx extend context correctly 2`] = `[]`;

exports[`renders components/masonry/demo/style-class.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-vertical"
  style="gap: 24px;"
>
  <div>
    <h4
      class="ant-typography css-var-test-id"
    >
      classNames and styles object
    </h4>
    <div
      class="ant-masonry custom-masonry-root css-var-test-id"
      style="height: 260px; border: 1px solid rgb(217, 217, 217); border-radius: 8px; padding: 16px; background-color: rgb(250, 250, 250);"
    >
      <div
        class="ant-masonry-item custom-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
        style="transform: scale(0.98); transition: transform 0.2s ease; opacity: 0.8; border: 1px solid rgb(204, 204, 204); --item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
      >
        <div
          class="ant-card ant-card-bordered ant-card-small css-var-test-id"
          style="height: 120px;"
        >
          <div
            class="ant-card-body"
          >
            1
          </div>
        </div>
      </div>
      <div
        class="ant-masonry-item custom-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
        style="transform: scale(0.98); transition: transform 0.2s ease; opacity: 0.8; border: 1px solid rgb(204, 204, 204); --item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
      >
        <div
          class="ant-card ant-card-bordered ant-card-small css-var-test-id"
          style="height: 80px;"
        >
          <div
            class="ant-card-body"
          >
            2
          </div>
        </div>
      </div>
      <div
        class="ant-masonry-item custom-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
        style="transform: scale(0.98); transition: transform 0.2s ease; opacity: 0.8; border: 1px solid rgb(204, 204, 204); --item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
      >
        <div
          class="ant-card ant-card-bordered ant-card-small css-var-test-id"
          style="height: 100px;"
        >
          <div
            class="ant-card-body"
          >
            3
          </div>
        </div>
      </div>
      <div
        class="ant-masonry-item custom-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
        style="transform: scale(0.98); transition: transform 0.2s ease; opacity: 0.8; border: 1px solid rgb(204, 204, 204); --item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
      >
        <div
          class="ant-card ant-card-bordered ant-card-small css-var-test-id"
          style="height: 60px;"
        >
          <div
            class="ant-card-body"
          >
            4
          </div>
        </div>
      </div>
      <div
        class="ant-masonry-item custom-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
        style="transform: scale(0.98); transition: transform 0.2s ease; opacity: 0.8; border: 1px solid rgb(204, 204, 204); --item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
      >
        <div
          class="ant-card ant-card-bordered ant-card-small css-var-test-id"
          style="height: 140px;"
        >
          <div
            class="ant-card-body"
          >
            5
          </div>
        </div>
      </div>
      <div
        class="ant-masonry-item custom-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
        style="transform: scale(0.98); transition: transform 0.2s ease; opacity: 0.8; border: 1px solid rgb(204, 204, 204); --item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
      >
        <div
          class="ant-card ant-card-bordered ant-card-small css-var-test-id"
          style="height: 90px;"
        >
          <div
            class="ant-card-body"
          >
            6
          </div>
        </div>
      </div>
      <div
        class="ant-masonry-item custom-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
        style="transform: scale(0.98); transition: transform 0.2s ease; opacity: 0.8; border: 1px solid rgb(204, 204, 204); --item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
      >
        <div
          class="ant-card ant-card-bordered ant-card-small css-var-test-id"
          style="height: 110px;"
        >
          <div
            class="ant-card-body"
          >
            7
          </div>
        </div>
      </div>
      <div
        class="ant-masonry-item custom-masonry-item ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
        style="transform: scale(0.98); transition: transform 0.2s ease; opacity: 0.8; border: 1px solid rgb(204, 204, 204); --item-width: calc((100% + 16px) / 4); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 16px); position: absolute;"
      >
        <div
          class="ant-card ant-card-bordered ant-card-small css-var-test-id"
          style="height: 70px;"
        >
          <div
            class="ant-card-body"
          >
            8
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
  />
  <div>
    <h4
      class="ant-typography css-var-test-id"
    >
      classNames and styles function
    </h4>
    <div
      class="ant-masonry dynamic-masonry-3-cols css-var-test-id"
      style="height: 280px; border: 2px solid rgb(24, 144, 255); border-radius: 12px; padding: 20px; background-color: rgb(240, 248, 255);"
    >
      <div
        class="ant-masonry-item dynamic-item-12px-gutter ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
        style="box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-radius: 6px; overflow: hidden; --item-width: calc((100% + 12px) / 3); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 12px); position: absolute;"
      >
        <div
          class="ant-card ant-card-bordered ant-card-small css-var-test-id"
          style="height: 120px;"
        >
          <div
            class="ant-card-body"
          >
            Item 1
          </div>
        </div>
      </div>
      <div
        class="ant-masonry-item dynamic-item-12px-gutter ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
        style="box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-radius: 6px; overflow: hidden; --item-width: calc((100% + 12px) / 3); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 12px); position: absolute;"
      >
        <div
          class="ant-card ant-card-bordered ant-card-small css-var-test-id"
          style="height: 80px;"
        >
          <div
            class="ant-card-body"
          >
            Item 2
          </div>
        </div>
      </div>
      <div
        class="ant-masonry-item dynamic-item-12px-gutter ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
        style="box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-radius: 6px; overflow: hidden; --item-width: calc((100% + 12px) / 3); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 12px); position: absolute;"
      >
        <div
          class="ant-card ant-card-bordered ant-card-small css-var-test-id"
          style="height: 100px;"
        >
          <div
            class="ant-card-body"
          >
            Item 3
          </div>
        </div>
      </div>
      <div
        class="ant-masonry-item dynamic-item-12px-gutter ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
        style="box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-radius: 6px; overflow: hidden; --item-width: calc((100% + 12px) / 3); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 12px); position: absolute;"
      >
        <div
          class="ant-card ant-card-bordered ant-card-small css-var-test-id"
          style="height: 60px;"
        >
          <div
            class="ant-card-body"
          >
            Item 4
          </div>
        </div>
      </div>
      <div
        class="ant-masonry-item dynamic-item-12px-gutter ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
        style="box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-radius: 6px; overflow: hidden; --item-width: calc((100% + 12px) / 3); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 12px); position: absolute;"
      >
        <div
          class="ant-card ant-card-bordered ant-card-small css-var-test-id"
          style="height: 140px;"
        >
          <div
            class="ant-card-body"
          >
            Item 5
          </div>
        </div>
      </div>
      <div
        class="ant-masonry-item dynamic-item-12px-gutter ant-masonry-item-fade-appear ant-masonry-item-fade-appear-start ant-masonry-item-fade"
        style="box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-radius: 6px; overflow: hidden; --item-width: calc((100% + 12px) / 3); inset-inline-start: calc(var(--item-width) * 0); width: calc(var(--item-width) - 12px); position: absolute;"
      >
        <div
          class="ant-card ant-card-bordered ant-card-small css-var-test-id"
          style="height: 90px;"
        >
          <div
            class="ant-card-body"
          >
            Item 6
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/masonry/demo/style-class.tsx extend context correctly 2`] = `[]`;
