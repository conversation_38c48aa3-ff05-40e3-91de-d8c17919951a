// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/splitter/demo/collapsible.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <div
    class="ant-splitter ant-splitter-horizontal css-var-test-id ant-splitter-css-var"
    style="box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); height: 200px;"
  >
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          First
        </h5>
      </div>
    </div>
    <div
      aria-valuemax="0"
      aria-valuemin="0"
      aria-valuenow="50"
      class="ant-splitter-bar"
      role="separator"
    >
      <div
        class="ant-splitter-bar-dragger ant-splitter-bar-dragger-disabled"
      />
    </div>
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Second
        </h5>
      </div>
    </div>
  </div>
  <div
    class="ant-splitter ant-splitter-vertical css-var-test-id ant-splitter-css-var"
    style="box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); height: 300px;"
  >
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          First
        </h5>
      </div>
    </div>
    <div
      aria-valuemax="0"
      aria-valuemin="0"
      aria-valuenow="50"
      class="ant-splitter-bar"
      role="separator"
    >
      <div
        class="ant-splitter-bar-dragger ant-splitter-bar-dragger-disabled"
      />
    </div>
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Second
        </h5>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/splitter/demo/collapsible.tsx extend context correctly 2`] = `[]`;

exports[`renders components/splitter/demo/collapsibleIcon.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-vertical"
  style="gap: 20px;"
>
  <div
    class="ant-flex css-var-test-id"
    style="gap: 5px;"
  >
    <p>
      ShowCollapsibleIcon: 
    </p>
    <div
      class="ant-radio-group ant-radio-group-outline css-var-test-id ant-radio-css-var"
    >
      <label
        class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio ant-wave-target"
        >
          <input
            class="ant-radio-input"
            name="test-id"
            type="radio"
            value="auto"
          />
          <span
            class="ant-radio-inner"
          />
        </span>
        <span
          class="ant-radio-label"
        >
          Auto
        </span>
      </label>
      <label
        class="ant-radio-wrapper ant-radio-wrapper-checked css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio ant-wave-target ant-radio-checked"
        >
          <input
            checked=""
            class="ant-radio-input"
            name="test-id"
            type="radio"
            value="true"
          />
          <span
            class="ant-radio-inner"
          />
        </span>
        <span
          class="ant-radio-label"
        >
          True
        </span>
      </label>
      <label
        class="ant-radio-wrapper css-var-test-id ant-radio-css-var"
      >
        <span
          class="ant-radio ant-wave-target"
        >
          <input
            class="ant-radio-input"
            name="test-id"
            type="radio"
            value="false"
          />
          <span
            class="ant-radio-inner"
          />
        </span>
        <span
          class="ant-radio-label"
        >
          False
        </span>
      </label>
    </div>
  </div>
  <div
    class="ant-splitter ant-splitter-horizontal css-var-test-id ant-splitter-css-var"
    style="height: 200px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);"
  >
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          First
        </h5>
      </div>
    </div>
    <div
      aria-valuemax="0"
      aria-valuemin="0"
      aria-valuenow="33"
      class="ant-splitter-bar"
      role="separator"
    >
      <div
        class="ant-splitter-bar-dragger ant-splitter-bar-dragger-disabled"
      />
    </div>
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Second
        </h5>
      </div>
    </div>
    <div
      aria-valuemax="0"
      aria-valuemin="0"
      aria-valuenow="67"
      class="ant-splitter-bar"
      role="separator"
    >
      <div
        class="ant-splitter-bar-dragger"
      />
    </div>
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Third
        </h5>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/splitter/demo/collapsibleIcon.tsx extend context correctly 2`] = `[]`;

exports[`renders components/splitter/demo/control.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <div
    class="ant-splitter ant-splitter-horizontal css-var-test-id ant-splitter-css-var"
    style="height: 200px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);"
  >
    <div
      class="ant-splitter-panel"
      style="flex-basis: 50%; flex-grow: 0;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          First
        </h5>
      </div>
    </div>
    <div
      aria-valuemax="0"
      aria-valuemin="0"
      aria-valuenow="50"
      class="ant-splitter-bar"
      role="separator"
    >
      <div
        class="ant-splitter-bar-dragger"
      />
    </div>
    <div
      class="ant-splitter-panel"
      style="flex-basis: 50%; flex-grow: 0;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Second
        </h5>
      </div>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-justify-space-between ant-flex-gap-middle"
  >
    <button
      aria-checked="true"
      class="ant-switch css-var-test-id ant-switch-checked"
      role="switch"
      type="button"
    >
      <div
        class="ant-switch-handle"
      />
      <span
        class="ant-switch-inner"
      >
        <span
          class="ant-switch-inner-checked"
        >
          Enabled
        </span>
        <span
          class="ant-switch-inner-unchecked"
        >
          Disabled
        </span>
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Reset
      </span>
    </button>
  </div>
</div>
`;

exports[`renders components/splitter/demo/control.tsx extend context correctly 2`] = `[]`;

exports[`renders components/splitter/demo/customize.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-splitter ant-splitter-horizontal css-var-test-id ant-splitter-css-var"
    style="height: 200px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);"
  >
    <div
      class="ant-splitter-panel"
      style="flex-basis: 40%; flex-grow: 0;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Panel 1
        </h5>
      </div>
    </div>
    <div
      aria-valuemax="0"
      aria-valuemin="0"
      aria-valuenow="40"
      class="ant-splitter-bar"
      role="separator"
    >
      <div
        class="ant-splitter-bar-dragger ant-splitter-bar-dragger-disabled ant-splitter-bar-dragger-customize"
      >
        <div
          class="ant-splitter-bar-dragger-icon"
        >
          <span
            aria-label="column-width"
            class="anticon anticon-column-width acss-jp9u09"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="column-width"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M180 176h-60c-4.4 0-8 3.6-8 8v656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V184c0-4.4-3.6-8-8-8zm724 0h-60c-4.4 0-8 3.6-8 8v656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V184c0-4.4-3.6-8-8-8zM785.3 504.3L657.7 403.6a7.23 7.23 0 00-11.7 5.7V476H378v-62.8c0-6-7-9.4-11.7-5.7L238.7 508.3a7.14 7.14 0 000 11.3l127.5 100.8c4.7 3.7 11.7.4 11.7-5.7V548h268v62.8c0 6 7 9.4 11.7 5.7l127.5-100.8c3.8-2.9 3.8-8.5.2-11.4z"
              />
            </svg>
          </span>
        </div>
      </div>
    </div>
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Panel 2
        </h5>
      </div>
    </div>
    <div
      aria-valuemax="0"
      aria-valuemin="0"
      aria-valuenow="70"
      class="ant-splitter-bar"
      role="separator"
    >
      <div
        class="ant-splitter-bar-dragger ant-splitter-bar-dragger-disabled ant-splitter-bar-dragger-customize"
      >
        <div
          class="ant-splitter-bar-dragger-icon"
        >
          <span
            aria-label="column-width"
            class="anticon anticon-column-width acss-jp9u09"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="column-width"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M180 176h-60c-4.4 0-8 3.6-8 8v656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V184c0-4.4-3.6-8-8-8zm724 0h-60c-4.4 0-8 3.6-8 8v656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V184c0-4.4-3.6-8-8-8zM785.3 504.3L657.7 403.6a7.23 7.23 0 00-11.7 5.7V476H378v-62.8c0-6-7-9.4-11.7-5.7L238.7 508.3a7.14 7.14 0 000 11.3l127.5 100.8c4.7 3.7 11.7.4 11.7-5.7V548h268v62.8c0 6 7 9.4 11.7 5.7l127.5-100.8c3.8-2.9 3.8-8.5.2-11.4z"
              />
            </svg>
          </span>
        </div>
      </div>
    </div>
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Panel 3
        </h5>
      </div>
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
    role="separator"
  />,
  <div
    class="ant-splitter ant-splitter-vertical css-var-test-id ant-splitter-css-var"
    style="height: 200px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);"
  >
    <div
      class="ant-splitter-panel"
      style="flex-basis: 40%; flex-grow: 0;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          First
        </h5>
      </div>
    </div>
    <div
      aria-valuemax="0"
      aria-valuemin="0"
      aria-valuenow="40"
      class="ant-splitter-bar"
      role="separator"
    >
      <div
        class="ant-splitter-bar-dragger ant-splitter-bar-dragger-disabled ant-splitter-bar-dragger-customize acss-13lz4mh"
      >
        <div
          class="ant-splitter-bar-dragger-icon"
        />
      </div>
    </div>
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Second
        </h5>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/splitter/demo/customize.tsx extend context correctly 2`] = `[]`;

exports[`renders components/splitter/demo/debug.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <h3
    class="ant-typography css-var-test-id"
  >
    [true, 0, false]
  </h3>
  <div
    class="ant-splitter ant-splitter-horizontal css-var-test-id ant-splitter-css-var"
    style="height: 200px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);"
  >
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Panel 1
        </h5>
      </div>
    </div>
    <div
      aria-valuemax="0"
      aria-valuemin="0"
      aria-valuenow="0"
      class="ant-splitter-bar"
      role="separator"
    >
      <div
        class="ant-splitter-bar-dragger"
      />
    </div>
    <div
      class="ant-splitter-panel ant-splitter-panel-hidden"
      style="flex-basis: 0px; flex-grow: 0;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Panel 2
        </h5>
      </div>
    </div>
    <div
      aria-valuemax="0"
      aria-valuemin="0"
      aria-valuenow="0"
      class="ant-splitter-bar"
      role="separator"
    >
      <div
        class="ant-splitter-bar-dragger ant-splitter-bar-dragger-disabled"
      />
    </div>
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Panel 3
        </h5>
      </div>
    </div>
  </div>
  <h3
    class="ant-typography css-var-test-id"
  >
    [false, 0, true]
  </h3>
  <div
    class="ant-splitter ant-splitter-horizontal css-var-test-id ant-splitter-css-var"
    style="height: 200px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);"
  >
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Panel 1
        </h5>
      </div>
    </div>
    <div
      aria-valuemax="0"
      aria-valuemin="0"
      aria-valuenow="0"
      class="ant-splitter-bar"
      role="separator"
    >
      <div
        class="ant-splitter-bar-dragger ant-splitter-bar-dragger-disabled"
      />
    </div>
    <div
      class="ant-splitter-panel ant-splitter-panel-hidden"
      style="flex-basis: 0px; flex-grow: 0;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Panel 2
        </h5>
      </div>
    </div>
    <div
      aria-valuemax="0"
      aria-valuemin="0"
      aria-valuenow="0"
      class="ant-splitter-bar"
      role="separator"
    >
      <div
        class="ant-splitter-bar-dragger"
      />
    </div>
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Panel 3
        </h5>
      </div>
    </div>
  </div>
  <h3
    class="ant-typography css-var-test-id"
  >
    Start have min & max
  </h3>
  <div
    class="ant-splitter ant-splitter-horizontal css-var-test-id ant-splitter-css-var"
    style="height: 200px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);"
  >
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Panel 1
        </h5>
      </div>
    </div>
    <div
      aria-valuemax="0"
      aria-valuemin="0"
      aria-valuenow="0"
      class="ant-splitter-bar"
      role="separator"
    >
      <div
        class="ant-splitter-bar-dragger"
      />
    </div>
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Panel 2
        </h5>
      </div>
    </div>
  </div>
  <h3
    class="ant-typography css-var-test-id"
  >
    End have min & max
  </h3>
  <div
    class="ant-splitter ant-splitter-horizontal css-var-test-id ant-splitter-css-var"
    style="height: 200px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);"
  >
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Panel 1
        </h5>
      </div>
    </div>
    <div
      aria-valuemax="0"
      aria-valuemin="0"
      aria-valuenow="50"
      class="ant-splitter-bar"
      role="separator"
    >
      <div
        class="ant-splitter-bar-dragger ant-splitter-bar-dragger-disabled"
      />
    </div>
    <div
      class="ant-splitter-panel"
      style="flex-basis: auto; flex-grow: 1;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
        style="height: 100%;"
      >
        <h5
          class="ant-typography ant-typography-secondary css-var-test-id"
          style="white-space: nowrap;"
        >
          Panel 2
        </h5>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/splitter/demo/debug.tsx extend context correctly 2`] = `[]`;

exports[`renders components/splitter/demo/group.tsx extend context correctly 1`] = `
<div
  class="ant-splitter ant-splitter-horizontal css-var-test-id ant-splitter-css-var"
  style="height: 300px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);"
>
  <div
    class="ant-splitter-panel"
    style="flex-basis: auto; flex-grow: 1;"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
      style="height: 100%;"
    >
      <h5
        class="ant-typography ant-typography-secondary css-var-test-id"
        style="white-space: nowrap;"
      >
        Left
      </h5>
    </div>
  </div>
  <div
    aria-valuemax="0"
    aria-valuemin="0"
    aria-valuenow="50"
    class="ant-splitter-bar"
    role="separator"
  >
    <div
      class="ant-splitter-bar-dragger"
    />
  </div>
  <div
    class="ant-splitter-panel"
    style="flex-basis: auto; flex-grow: 1;"
  >
    <div
      class="ant-splitter ant-splitter-vertical css-var-test-id ant-splitter-css-var"
    >
      <div
        class="ant-splitter-panel"
        style="flex-basis: auto; flex-grow: 1;"
      >
        <div
          class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
          style="height: 100%;"
        >
          <h5
            class="ant-typography ant-typography-secondary css-var-test-id"
            style="white-space: nowrap;"
          >
            Top
          </h5>
        </div>
      </div>
      <div
        aria-valuemax="0"
        aria-valuemin="0"
        aria-valuenow="50"
        class="ant-splitter-bar"
        role="separator"
      >
        <div
          class="ant-splitter-bar-dragger"
        />
      </div>
      <div
        class="ant-splitter-panel"
        style="flex-basis: auto; flex-grow: 1;"
      >
        <div
          class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
          style="height: 100%;"
        >
          <h5
            class="ant-typography ant-typography-secondary css-var-test-id"
            style="white-space: nowrap;"
          >
            Bottom
          </h5>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/splitter/demo/group.tsx extend context correctly 2`] = `[]`;

exports[`renders components/splitter/demo/lazy.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  style="width: 100%;"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-splitter ant-splitter-horizontal css-var-test-id ant-splitter-css-var"
      style="height: 200px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);"
    >
      <div
        class="ant-splitter-panel"
        style="flex-basis: 40%; flex-grow: 0;"
      >
        <div
          class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
          style="height: 100%;"
        >
          <h5
            class="ant-typography ant-typography-secondary css-var-test-id"
            style="white-space: nowrap;"
          >
            First
          </h5>
        </div>
      </div>
      <div
        aria-valuemax="0"
        aria-valuemin="0"
        aria-valuenow="40"
        class="ant-splitter-bar"
        role="separator"
      >
        <div
          class="ant-splitter-bar-preview"
          style="--ant-splitter-bar-preview-offset: 0px;"
        />
        <div
          class="ant-splitter-bar-dragger ant-splitter-bar-dragger-disabled"
        />
      </div>
      <div
        class="ant-splitter-panel"
        style="flex-basis: auto; flex-grow: 1;"
      >
        <div
          class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
          style="height: 100%;"
        >
          <h5
            class="ant-typography ant-typography-secondary css-var-test-id"
            style="white-space: nowrap;"
          >
            Second
          </h5>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-splitter ant-splitter-vertical css-var-test-id ant-splitter-css-var"
      style="height: 200px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);"
    >
      <div
        class="ant-splitter-panel"
        style="flex-basis: 40%; flex-grow: 0;"
      >
        <div
          class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
          style="height: 100%;"
        >
          <h5
            class="ant-typography ant-typography-secondary css-var-test-id"
            style="white-space: nowrap;"
          >
            First
          </h5>
        </div>
      </div>
      <div
        aria-valuemax="0"
        aria-valuemin="0"
        aria-valuenow="40"
        class="ant-splitter-bar"
        role="separator"
      >
        <div
          class="ant-splitter-bar-preview"
          style="--ant-splitter-bar-preview-offset: 0px;"
        />
        <div
          class="ant-splitter-bar-dragger ant-splitter-bar-dragger-disabled"
        />
      </div>
      <div
        class="ant-splitter-panel"
        style="flex-basis: auto; flex-grow: 1;"
      >
        <div
          class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
          style="height: 100%;"
        >
          <h5
            class="ant-typography ant-typography-secondary css-var-test-id"
            style="white-space: nowrap;"
          >
            Second
          </h5>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/splitter/demo/lazy.tsx extend context correctly 2`] = `[]`;

exports[`renders components/splitter/demo/multiple.tsx extend context correctly 1`] = `
<div
  class="ant-splitter ant-splitter-horizontal css-var-test-id ant-splitter-css-var"
  style="height: 200px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);"
>
  <div
    class="ant-splitter-panel"
    style="flex-basis: auto; flex-grow: 1;"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
      style="height: 100%;"
    >
      <h5
        class="ant-typography ant-typography-secondary css-var-test-id"
        style="white-space: nowrap;"
      >
        Panel 1
      </h5>
    </div>
  </div>
  <div
    aria-valuemax="0"
    aria-valuemin="0"
    aria-valuenow="33"
    class="ant-splitter-bar"
    role="separator"
  >
    <div
      class="ant-splitter-bar-dragger"
    />
  </div>
  <div
    class="ant-splitter-panel"
    style="flex-basis: auto; flex-grow: 1;"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
      style="height: 100%;"
    >
      <h5
        class="ant-typography ant-typography-secondary css-var-test-id"
        style="white-space: nowrap;"
      >
        Panel 2
      </h5>
    </div>
  </div>
  <div
    aria-valuemax="0"
    aria-valuemin="0"
    aria-valuenow="67"
    class="ant-splitter-bar"
    role="separator"
  >
    <div
      class="ant-splitter-bar-dragger"
    />
  </div>
  <div
    class="ant-splitter-panel"
    style="flex-basis: auto; flex-grow: 1;"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
      style="height: 100%;"
    >
      <h5
        class="ant-typography ant-typography-secondary css-var-test-id"
        style="white-space: nowrap;"
      >
        Panel 3
      </h5>
    </div>
  </div>
</div>
`;

exports[`renders components/splitter/demo/multiple.tsx extend context correctly 2`] = `[]`;

exports[`renders components/splitter/demo/nested-in-tabs.tsx extend context correctly 1`] = `
<div
  class="ant-tabs ant-tabs-top css-var-test-id ant-tabs-css-var"
>
  <div
    aria-orientation="horizontal"
    class="ant-tabs-nav"
    role="tablist"
  >
    <div
      class="ant-tabs-nav-wrap"
    >
      <div
        class="ant-tabs-nav-list"
        style="transform: translate(0px, 0px);"
      >
        <div
          class="ant-tabs-tab ant-tabs-tab-active"
          data-node-key="1"
        >
          <div
            aria-controls="rc-tabs-test-panel-1"
            aria-selected="true"
            class="ant-tabs-tab-btn"
            id="rc-tabs-test-tab-1"
            role="tab"
            tabindex="0"
          >
            General
          </div>
        </div>
        <div
          class="ant-tabs-tab"
          data-node-key="2"
        >
          <div
            aria-controls="rc-tabs-test-panel-2"
            aria-selected="false"
            class="ant-tabs-tab-btn"
            id="rc-tabs-test-tab-2"
            role="tab"
            tabindex="-1"
          >
            Splitter Tab
          </div>
        </div>
        <div
          class="ant-tabs-ink-bar ant-tabs-ink-bar-animated"
        />
      </div>
    </div>
    <div
      class="ant-tabs-nav-operations ant-tabs-nav-operations-hidden"
    >
      <button
        aria-controls="rc-tabs-test-more-popup"
        aria-expanded="false"
        aria-haspopup="listbox"
        class="ant-tabs-nav-more"
        id="rc-tabs-test-more"
        style="visibility: hidden; order: 1;"
        type="button"
      >
        <span
          aria-label="ellipsis"
          class="anticon anticon-ellipsis"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="ellipsis"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"
            />
          </svg>
        </span>
      </button>
      <div
        class="ant-tabs-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-tabs-css-var ant-tabs-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <ul
          aria-label="expanded dropdown"
          class="ant-tabs-dropdown-menu ant-tabs-dropdown-menu-root ant-tabs-dropdown-menu-vertical"
          data-menu-list="true"
          id="rc-tabs-test-more-popup"
          role="listbox"
          tabindex="-1"
        />
        <div
          aria-hidden="true"
          style="display: none;"
        />
      </div>
    </div>
  </div>
  <div
    class="ant-tabs-content-holder"
  >
    <div
      class="ant-tabs-content ant-tabs-content-top"
    >
      <div
        aria-hidden="false"
        aria-labelledby="rc-tabs-test-tab-1"
        class="ant-tabs-tabpane ant-tabs-tabpane-active"
        id="rc-tabs-test-panel-1"
        role="tabpanel"
        tabindex="0"
      >
        Content of Tab Pane 1
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/splitter/demo/nested-in-tabs.tsx extend context correctly 2`] = `[]`;

exports[`renders components/splitter/demo/size.tsx extend context correctly 1`] = `
<div
  class="ant-splitter ant-splitter-horizontal css-var-test-id ant-splitter-css-var"
  style="height: 200px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);"
>
  <div
    class="ant-splitter-panel"
    style="flex-basis: 40%; flex-grow: 0;"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
      style="height: 100%;"
    >
      <h5
        class="ant-typography ant-typography-secondary css-var-test-id"
        style="white-space: nowrap;"
      >
        First
      </h5>
    </div>
  </div>
  <div
    aria-valuemax="0"
    aria-valuemin="0"
    aria-valuenow="40"
    class="ant-splitter-bar"
    role="separator"
  >
    <div
      class="ant-splitter-bar-dragger ant-splitter-bar-dragger-disabled"
    />
  </div>
  <div
    class="ant-splitter-panel"
    style="flex-basis: auto; flex-grow: 1;"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
      style="height: 100%;"
    >
      <h5
        class="ant-typography ant-typography-secondary css-var-test-id"
        style="white-space: nowrap;"
      >
        Second
      </h5>
    </div>
  </div>
</div>
`;

exports[`renders components/splitter/demo/size.tsx extend context correctly 2`] = `[]`;

exports[`renders components/splitter/demo/vertical.tsx extend context correctly 1`] = `
<div
  class="ant-splitter ant-splitter-vertical css-var-test-id ant-splitter-css-var"
  style="height: 300px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);"
>
  <div
    class="ant-splitter-panel"
    style="flex-basis: auto; flex-grow: 1;"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
      style="height: 100%;"
    >
      <h5
        class="ant-typography ant-typography-secondary css-var-test-id"
        style="white-space: nowrap;"
      >
        First
      </h5>
    </div>
  </div>
  <div
    aria-valuemax="0"
    aria-valuemin="0"
    aria-valuenow="50"
    class="ant-splitter-bar"
    role="separator"
  >
    <div
      class="ant-splitter-bar-dragger"
    />
  </div>
  <div
    class="ant-splitter-panel"
    style="flex-basis: auto; flex-grow: 1;"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
      style="height: 100%;"
    >
      <h5
        class="ant-typography ant-typography-secondary css-var-test-id"
        style="white-space: nowrap;"
      >
        Second
      </h5>
    </div>
  </div>
</div>
`;

exports[`renders components/splitter/demo/vertical.tsx extend context correctly 2`] = `[]`;
