// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/watermark/demo/basic.tsx correctly 1`] = `
<div
  class=""
  style="position:relative;overflow:hidden"
>
  <div
    style="height:500px"
  />
</div>
`;

exports[`renders components/watermark/demo/custom.tsx correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-gap-middle"
>
  <div
    class=""
    style="position:relative;overflow:hidden"
  >
    <article
      class="ant-typography css-var-test-id"
    >
      <div
        class="ant-typography css-var-test-id"
      >
        The light-speed iteration of the digital world makes products more complex. However, human consciousness and attention resources are limited. Facing this design contradiction, the pursuit of natural interaction will be the consistent direction of Ant Design.
      </div>
      <div
        class="ant-typography css-var-test-id"
      >
        Natural user cognition: According to cognitive psychology, about 80% of external information is obtained through visual channels. The most important visual elements in the interface design, including layout, colors, illustrations, icons, etc., should fully absorb the laws of nature, thereby reducing the user's cognitive cost and bringing authentic and smooth feelings. In some scenarios, opportunely adding other sensory channels such as hearing, touch can create a richer and more natural product experience.
      </div>
      <div
        class="ant-typography css-var-test-id"
      >
        Natural user behavior: In the interaction with the system, the designer should fully understand the relationship between users, system roles, and task objectives, and also contextually organize system functions and services. At the same time, a series of methods such as behavior analysis, artificial intelligence and sensors could be applied to assist users to make effective decisions and reduce extra operations of users, to save users' mental and physical resources and make human-computer interaction more natural.
      </div>
    </article>
    <img
      alt="img"
      draggable="false"
      src="https://gw.alipayobjects.com/mdn/rms_08e378/afts/img/A*zx7LTI_ECSAAAAAAAAAAAABkARQnAQ"
      style="z-index:10;width:100%;max-width:800px;position:relative"
    />
  </div>
  <form
    class="ant-form ant-form-vertical css-var-test-id ant-form-css-var"
    style="width:280px;flex-shrink:0;border-inline-start:1px solid #eee;padding-inline-start:16px"
  >
    <div
      class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-vertical"
    >
      <div
        class="ant-row ant-form-item-row css-var-test-id"
      >
        <div
          class="ant-col ant-form-item-label css-var-test-id"
        >
          <label
            class=""
            for="content"
            title="Content"
          >
            Content
          </label>
        </div>
        <div
          class="ant-col ant-form-item-control css-var-test-id"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <input
                class="ant-input ant-input-outlined css-var-test-id ant-input-css-var"
                id="content"
                placeholder="请输入"
                type="text"
                value="Ant Design"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-vertical"
    >
      <div
        class="ant-row ant-form-item-row css-var-test-id"
      >
        <div
          class="ant-col ant-form-item-label css-var-test-id"
        >
          <label
            class=""
            for="color"
            title="Color"
          >
            Color
          </label>
        </div>
        <div
          class="ant-col ant-form-item-control css-var-test-id"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                aria-describedby="test-id"
                class="ant-color-picker-trigger css-var-test-id ant-color-picker-css-var"
                id="color"
              >
                <div
                  class="ant-color-picker-color-block"
                >
                  <div
                    class="ant-color-picker-color-block-inner"
                    style="background:rgba(0,0,0,0.15)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-vertical"
    >
      <div
        class="ant-row ant-form-item-row css-var-test-id"
      >
        <div
          class="ant-col ant-form-item-label css-var-test-id"
        >
          <label
            class=""
            for="fontSize"
            title="FontSize"
          >
            FontSize
          </label>
        </div>
        <div
          class="ant-col ant-form-item-control css-var-test-id"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                class="ant-slider css-var-test-id ant-slider-horizontal"
                id="fontSize"
              >
                <div
                  class="ant-slider-rail"
                />
                <div
                  class="ant-slider-track"
                  style="left:0%;width:15.151515151515152%"
                />
                <div
                  class="ant-slider-step"
                />
                <div
                  aria-describedby="test-id"
                  aria-disabled="false"
                  aria-orientation="horizontal"
                  aria-valuemax="100"
                  aria-valuemin="1"
                  aria-valuenow="16"
                  class="ant-slider-handle"
                  role="slider"
                  style="left:15.151515151515152%;transform:translateX(-50%)"
                  tabindex="0"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-vertical"
    >
      <div
        class="ant-row ant-form-item-row css-var-test-id"
      >
        <div
          class="ant-col ant-form-item-label css-var-test-id"
        >
          <label
            class=""
            for="zIndex"
            title="zIndex"
          >
            zIndex
          </label>
        </div>
        <div
          class="ant-col ant-form-item-control css-var-test-id"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                class="ant-slider css-var-test-id ant-slider-horizontal"
                id="zIndex"
              >
                <div
                  class="ant-slider-rail"
                />
                <div
                  class="ant-slider-track"
                  style="left:0%;width:11%"
                />
                <div
                  class="ant-slider-step"
                />
                <div
                  aria-describedby="test-id"
                  aria-disabled="false"
                  aria-orientation="horizontal"
                  aria-valuemax="100"
                  aria-valuemin="0"
                  aria-valuenow="11"
                  class="ant-slider-handle"
                  role="slider"
                  style="left:11%;transform:translateX(-50%)"
                  tabindex="0"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-vertical"
    >
      <div
        class="ant-row ant-form-item-row css-var-test-id"
      >
        <div
          class="ant-col ant-form-item-label css-var-test-id"
        >
          <label
            class=""
            for="rotate"
            title="Rotate"
          >
            Rotate
          </label>
        </div>
        <div
          class="ant-col ant-form-item-control css-var-test-id"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                class="ant-slider css-var-test-id ant-slider-horizontal"
                id="rotate"
              >
                <div
                  class="ant-slider-rail"
                />
                <div
                  class="ant-slider-track"
                  style="left:0%;width:43.888888888888886%"
                />
                <div
                  class="ant-slider-step"
                />
                <div
                  aria-describedby="test-id"
                  aria-disabled="false"
                  aria-orientation="horizontal"
                  aria-valuemax="180"
                  aria-valuemin="-180"
                  aria-valuenow="-22"
                  class="ant-slider-handle"
                  role="slider"
                  style="left:43.888888888888886%;transform:translateX(-50%)"
                  tabindex="0"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-vertical"
      style="margin-bottom:0"
    >
      <div
        class="ant-row ant-form-item-row css-var-test-id"
      >
        <div
          class="ant-col ant-form-item-label css-var-test-id"
        >
          <label
            class=""
            title="Gap"
          >
            Gap
          </label>
        </div>
        <div
          class="ant-col ant-form-item-control css-var-test-id"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                class="ant-flex css-var-test-id ant-flex-gap-small"
              >
                <div
                  class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-vertical"
                >
                  <div
                    class="ant-row ant-form-item-row css-var-test-id"
                  >
                    <div
                      class="ant-col ant-form-item-control css-var-test-id"
                    >
                      <div
                        class="ant-form-item-control-input"
                      >
                        <div
                          class="ant-form-item-control-input-content"
                        >
                          <div
                            class="ant-input-number ant-input-number-in-form-item ant-input-number-outlined css-var-test-id ant-input-number-css-var"
                            style="width:100%"
                          >
                            <div
                              class="ant-input-number-handler-wrap"
                            >
                              <span
                                aria-disabled="false"
                                aria-label="Increase Value"
                                class="ant-input-number-handler ant-input-number-handler-up"
                                role="button"
                                unselectable="on"
                              >
                                <span
                                  aria-label="up"
                                  class="anticon anticon-up ant-input-number-handler-up-inner"
                                  role="img"
                                >
                                  <svg
                                    aria-hidden="true"
                                    data-icon="up"
                                    fill="currentColor"
                                    focusable="false"
                                    height="1em"
                                    viewBox="64 64 896 896"
                                    width="1em"
                                  >
                                    <path
                                      d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                                    />
                                  </svg>
                                </span>
                              </span>
                              <span
                                aria-disabled="false"
                                aria-label="Decrease Value"
                                class="ant-input-number-handler ant-input-number-handler-down"
                                role="button"
                                unselectable="on"
                              >
                                <span
                                  aria-label="down"
                                  class="anticon anticon-down ant-input-number-handler-down-inner"
                                  role="img"
                                >
                                  <svg
                                    aria-hidden="true"
                                    data-icon="down"
                                    fill="currentColor"
                                    focusable="false"
                                    height="1em"
                                    viewBox="64 64 896 896"
                                    width="1em"
                                  >
                                    <path
                                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                                    />
                                  </svg>
                                </span>
                              </span>
                            </div>
                            <div
                              class="ant-input-number-input-wrap"
                            >
                              <input
                                aria-valuenow="100"
                                autocomplete="off"
                                class="ant-input-number-input"
                                id="gap_0"
                                placeholder="gapX"
                                role="spinbutton"
                                step="1"
                                value="100"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-vertical"
                >
                  <div
                    class="ant-row ant-form-item-row css-var-test-id"
                  >
                    <div
                      class="ant-col ant-form-item-control css-var-test-id"
                    >
                      <div
                        class="ant-form-item-control-input"
                      >
                        <div
                          class="ant-form-item-control-input-content"
                        >
                          <div
                            class="ant-input-number ant-input-number-in-form-item ant-input-number-outlined css-var-test-id ant-input-number-css-var"
                            style="width:100%"
                          >
                            <div
                              class="ant-input-number-handler-wrap"
                            >
                              <span
                                aria-disabled="false"
                                aria-label="Increase Value"
                                class="ant-input-number-handler ant-input-number-handler-up"
                                role="button"
                                unselectable="on"
                              >
                                <span
                                  aria-label="up"
                                  class="anticon anticon-up ant-input-number-handler-up-inner"
                                  role="img"
                                >
                                  <svg
                                    aria-hidden="true"
                                    data-icon="up"
                                    fill="currentColor"
                                    focusable="false"
                                    height="1em"
                                    viewBox="64 64 896 896"
                                    width="1em"
                                  >
                                    <path
                                      d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                                    />
                                  </svg>
                                </span>
                              </span>
                              <span
                                aria-disabled="false"
                                aria-label="Decrease Value"
                                class="ant-input-number-handler ant-input-number-handler-down"
                                role="button"
                                unselectable="on"
                              >
                                <span
                                  aria-label="down"
                                  class="anticon anticon-down ant-input-number-handler-down-inner"
                                  role="img"
                                >
                                  <svg
                                    aria-hidden="true"
                                    data-icon="down"
                                    fill="currentColor"
                                    focusable="false"
                                    height="1em"
                                    viewBox="64 64 896 896"
                                    width="1em"
                                  >
                                    <path
                                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                                    />
                                  </svg>
                                </span>
                              </span>
                            </div>
                            <div
                              class="ant-input-number-input-wrap"
                            >
                              <input
                                aria-valuenow="100"
                                autocomplete="off"
                                class="ant-input-number-input"
                                id="gap_1"
                                placeholder="gapY"
                                role="spinbutton"
                                step="1"
                                value="100"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-vertical"
      style="margin-bottom:0"
    >
      <div
        class="ant-row ant-form-item-row css-var-test-id"
      >
        <div
          class="ant-col ant-form-item-label css-var-test-id"
        >
          <label
            class=""
            title="Offset"
          >
            Offset
          </label>
        </div>
        <div
          class="ant-col ant-form-item-control css-var-test-id"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                class="ant-flex css-var-test-id ant-flex-gap-small"
              >
                <div
                  class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-vertical"
                >
                  <div
                    class="ant-row ant-form-item-row css-var-test-id"
                  >
                    <div
                      class="ant-col ant-form-item-control css-var-test-id"
                    >
                      <div
                        class="ant-form-item-control-input"
                      >
                        <div
                          class="ant-form-item-control-input-content"
                        >
                          <div
                            class="ant-input-number ant-input-number-in-form-item ant-input-number-outlined css-var-test-id ant-input-number-css-var"
                            style="width:100%"
                          >
                            <div
                              class="ant-input-number-handler-wrap"
                            >
                              <span
                                aria-disabled="false"
                                aria-label="Increase Value"
                                class="ant-input-number-handler ant-input-number-handler-up"
                                role="button"
                                unselectable="on"
                              >
                                <span
                                  aria-label="up"
                                  class="anticon anticon-up ant-input-number-handler-up-inner"
                                  role="img"
                                >
                                  <svg
                                    aria-hidden="true"
                                    data-icon="up"
                                    fill="currentColor"
                                    focusable="false"
                                    height="1em"
                                    viewBox="64 64 896 896"
                                    width="1em"
                                  >
                                    <path
                                      d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                                    />
                                  </svg>
                                </span>
                              </span>
                              <span
                                aria-disabled="false"
                                aria-label="Decrease Value"
                                class="ant-input-number-handler ant-input-number-handler-down"
                                role="button"
                                unselectable="on"
                              >
                                <span
                                  aria-label="down"
                                  class="anticon anticon-down ant-input-number-handler-down-inner"
                                  role="img"
                                >
                                  <svg
                                    aria-hidden="true"
                                    data-icon="down"
                                    fill="currentColor"
                                    focusable="false"
                                    height="1em"
                                    viewBox="64 64 896 896"
                                    width="1em"
                                  >
                                    <path
                                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                                    />
                                  </svg>
                                </span>
                              </span>
                            </div>
                            <div
                              class="ant-input-number-input-wrap"
                            >
                              <input
                                autocomplete="off"
                                class="ant-input-number-input"
                                id="offset_0"
                                placeholder="offsetLeft"
                                role="spinbutton"
                                step="1"
                                value=""
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-vertical"
                >
                  <div
                    class="ant-row ant-form-item-row css-var-test-id"
                  >
                    <div
                      class="ant-col ant-form-item-control css-var-test-id"
                    >
                      <div
                        class="ant-form-item-control-input"
                      >
                        <div
                          class="ant-form-item-control-input-content"
                        >
                          <div
                            class="ant-input-number ant-input-number-in-form-item ant-input-number-outlined css-var-test-id ant-input-number-css-var"
                            style="width:100%"
                          >
                            <div
                              class="ant-input-number-handler-wrap"
                            >
                              <span
                                aria-disabled="false"
                                aria-label="Increase Value"
                                class="ant-input-number-handler ant-input-number-handler-up"
                                role="button"
                                unselectable="on"
                              >
                                <span
                                  aria-label="up"
                                  class="anticon anticon-up ant-input-number-handler-up-inner"
                                  role="img"
                                >
                                  <svg
                                    aria-hidden="true"
                                    data-icon="up"
                                    fill="currentColor"
                                    focusable="false"
                                    height="1em"
                                    viewBox="64 64 896 896"
                                    width="1em"
                                  >
                                    <path
                                      d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                                    />
                                  </svg>
                                </span>
                              </span>
                              <span
                                aria-disabled="false"
                                aria-label="Decrease Value"
                                class="ant-input-number-handler ant-input-number-handler-down"
                                role="button"
                                unselectable="on"
                              >
                                <span
                                  aria-label="down"
                                  class="anticon anticon-down ant-input-number-handler-down-inner"
                                  role="img"
                                >
                                  <svg
                                    aria-hidden="true"
                                    data-icon="down"
                                    fill="currentColor"
                                    focusable="false"
                                    height="1em"
                                    viewBox="64 64 896 896"
                                    width="1em"
                                  >
                                    <path
                                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                                    />
                                  </svg>
                                </span>
                              </span>
                            </div>
                            <div
                              class="ant-input-number-input-wrap"
                            >
                              <input
                                autocomplete="off"
                                class="ant-input-number-input"
                                id="offset_1"
                                placeholder="offsetTop"
                                role="spinbutton"
                                step="1"
                                value=""
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>
`;

exports[`renders components/watermark/demo/image.tsx correctly 1`] = `
<div
  class=""
  style="position:relative;overflow:hidden"
>
  <div
    style="height:500px"
  />
</div>
`;

exports[`renders components/watermark/demo/multi-line.tsx correctly 1`] = `
<div
  class=""
  style="position:relative;overflow:hidden"
>
  <div
    style="height:500px"
  />
</div>
`;

exports[`renders components/watermark/demo/portal.tsx correctly 1`] = `
Array [
  <div
    class="ant-flex css-var-test-id ant-flex-gap-middle"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Show in Modal
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Show in Drawer
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Not Show in Drawer
      </span>
    </button>
  </div>,
  <div
    class=""
    style="position:relative;overflow:hidden"
  />,
  <div
    class=""
    style="position:relative;overflow:hidden"
  />,
]
`;
