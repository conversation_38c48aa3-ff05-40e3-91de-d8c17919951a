{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "files": {"ignoreUnknown": true, "includes": ["**", "!.dumi/tmp*", "!.dumi/scripts/clarity.js", "!dist/*", "!es/**/*", "!lib/**/*", "!_site/**/*", "!node_modules", "!server", "!scripts/previewEditor/**/*", "!**/*.tmp", "!package.json", "!components/style/antd.css"]}, "formatter": {"enabled": true, "indentStyle": "space", "lineWidth": 100, "indentWidth": 2}, "javascript": {"jsxRuntime": "reactClassic", "formatter": {"quoteStyle": "single"}}, "assist": {"actions": {"source": {"organizeImports": "off"}}}, "linter": {"rules": {"style": {"useImportType": "off", "useNumberNamespace": "off", "useNodejsImportProtocol": "off", "noNonNullAssertion": "off", "noUnusedTemplateLiteral": "off"}, "complexity": {"noUselessTypeConstraint": "off", "noForEach": "off", "useDateNow": "off", "noImportantStyles": "off", "useIndexOf": "off", "useOptionalChain": "off"}, "correctness": {"useUniqueElementIds": "off", "useExhaustiveDependencies": "off", "useHookAtTopLevel": "off", "noUnusedFunctionParameters": "off", "noUnusedVariables": "off"}, "suspicious": {"noTsIgnore": "off", "noGlobalIsNan": "off", "noGlobalIsFinite": "off", "noExplicitAny": "off", "noArrayIndexKey": "off", "noConfusingVoidType": "off", "noThenProperty": "off", "noTemplateCurlyInString": "off", "useIterableCallbackReturn": "off", "noUnknownAtRules": "off"}, "performance": {"noDelete": "off", "noAccumulatingSpread": "off", "noDynamicNamespaceImportAccess": "off"}, "a11y": {"noAriaHiddenOnFocusable": "off", "noLabelWithoutControl": "off", "useFocusableInteractive": "off", "useKeyWithClickEvents": "off", "useSemanticElements": "off", "noStaticElementInteractions": "off", "useAriaPropsSupportedByRole": "off", "useAriaPropsForRole": "off"}}}, "css": {"formatter": {"quoteStyle": "single"}}, "overrides": [{"includes": ["**/*.test.ts", "**/*.test.tsx", "tests/**/*", "scripts/**/*", ".dumi/**/*"], "linter": {"rules": {"style": {"noParameterAssign": "off"}, "suspicious": {"noThenProperty": "off", "noImplicitAnyLet": "off"}, "complexity": {"noUselessFragments": "off"}, "a11y": {"useValidAnchor": "off", "useAnchorContent": "off", "useKeyWithClickEvents": "off"}}}}, {"includes": ["components/*/demo/*"], "linter": {"rules": {"correctness": {"noVoidTypeReturn": "off"}, "a11y": {"useValidAnchor": "off", "useAnchorContent": "off", "useKeyWithClickEvents": "off"}}}}]}